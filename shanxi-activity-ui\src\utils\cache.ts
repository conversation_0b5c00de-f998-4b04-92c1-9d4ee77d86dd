// 缓存管理工具

// 缓存项接口
interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiry?: number
}

// 缓存配置
interface CacheConfig {
  prefix?: string
  defaultExpiry?: number // 默认过期时间（毫秒）
  storage?: Storage
}

// 内存缓存
class MemoryCache {
  private cache = new Map<string, CacheItem>()
  private config: Required<CacheConfig>

  constructor(config: CacheConfig = {}) {
    this.config = {
      prefix: 'app_cache_',
      defaultExpiry: 5 * 60 * 1000, // 5分钟
      storage: localStorage,
      ...config
    }
  }

  // 生成缓存键
  private getKey(key: string): string {
    return `${this.config.prefix}${key}`
  }

  // 检查是否过期
  private isExpired(item: CacheItem): boolean {
    if (!item.expiry) return false
    return Date.now() > item.timestamp + item.expiry
  }

  // 设置缓存
  set<T>(key: string, data: T, expiry?: number): void {
    const cacheKey = this.getKey(key)
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiry: expiry || this.config.defaultExpiry
    }
    
    this.cache.set(cacheKey, item)
  }

  // 获取缓存
  get<T>(key: string): T | null {
    const cacheKey = this.getKey(key)
    const item = this.cache.get(cacheKey)
    
    if (!item) return null
    
    if (this.isExpired(item)) {
      this.cache.delete(cacheKey)
      return null
    }
    
    return item.data as T
  }

  // 删除缓存
  delete(key: string): boolean {
    const cacheKey = this.getKey(key)
    return this.cache.delete(cacheKey)
  }

  // 清空缓存
  clear(): void {
    this.cache.clear()
  }

  // 获取缓存大小
  size(): number {
    return this.cache.size
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (this.isExpired(item)) {
        this.cache.delete(key)
      }
    }
  }

  // 获取所有键
  keys(): string[] {
    return Array.from(this.cache.keys()).map(key => 
      key.replace(this.config.prefix, '')
    )
  }
}

// 持久化缓存
class PersistentCache extends MemoryCache {
  constructor(config: CacheConfig = {}) {
    super(config)
    this.loadFromStorage()
  }

  // 从存储加载缓存
  private loadFromStorage(): void {
    try {
      const keys = Object.keys(this.config.storage)
      for (const key of keys) {
        if (key.startsWith(this.config.prefix)) {
          const value = this.config.storage.getItem(key)
          if (value) {
            const item: CacheItem = JSON.parse(value)
            if (!this.isExpired(item)) {
              this.cache.set(key, item)
            } else {
              this.config.storage.removeItem(key)
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to load cache from storage:', error)
    }
  }

  // 保存到存储
  private saveToStorage(key: string, item: CacheItem): void {
    try {
      this.config.storage.setItem(key, JSON.stringify(item))
    } catch (error) {
      console.error('Failed to save cache to storage:', error)
      // 存储空间不足时清理过期缓存
      this.cleanup()
      try {
        this.config.storage.setItem(key, JSON.stringify(item))
      } catch (retryError) {
        console.error('Failed to save cache after cleanup:', retryError)
      }
    }
  }

  // 重写set方法
  set<T>(key: string, data: T, expiry?: number): void {
    super.set(key, data, expiry)
    
    const cacheKey = this.getKey(key)
    const item = this.cache.get(cacheKey)
    if (item) {
      this.saveToStorage(cacheKey, item)
    }
  }

  // 重写delete方法
  delete(key: string): boolean {
    const cacheKey = this.getKey(key)
    const result = super.delete(key)
    this.config.storage.removeItem(cacheKey)
    return result
  }

  // 重写clear方法
  clear(): void {
    const keys = this.keys()
    keys.forEach(key => {
      this.config.storage.removeItem(this.getKey(key))
    })
    super.clear()
  }

  // 重写cleanup方法
  cleanup(): void {
    const keys = this.keys()
    keys.forEach(key => {
      const cacheKey = this.getKey(key)
      const item = this.cache.get(cacheKey)
      if (item && this.isExpired(item)) {
        this.cache.delete(cacheKey)
        this.config.storage.removeItem(cacheKey)
      }
    })
  }
}

// LRU缓存
class LRUCache<T = any> {
  private cache = new Map<string, T>()
  private maxSize: number

  constructor(maxSize = 100) {
    this.maxSize = maxSize
  }

  get(key: string): T | undefined {
    const value = this.cache.get(key)
    if (value !== undefined) {
      // 移到最后（最近使用）
      this.cache.delete(key)
      this.cache.set(key, value)
    }
    return value
  }

  set(key: string, value: T): void {
    if (this.cache.has(key)) {
      this.cache.delete(key)
    } else if (this.cache.size >= this.maxSize) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    this.cache.set(key, value)
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }
}

// 缓存管理器
class CacheManager {
  private memoryCache: MemoryCache
  private persistentCache: PersistentCache
  private lruCache: LRUCache

  constructor() {
    this.memoryCache = new MemoryCache({
      prefix: 'memory_',
      defaultExpiry: 5 * 60 * 1000 // 5分钟
    })
    
    this.persistentCache = new PersistentCache({
      prefix: 'persistent_',
      defaultExpiry: 24 * 60 * 60 * 1000, // 24小时
      storage: localStorage
    })
    
    this.lruCache = new LRUCache(50)
    
    // 定期清理过期缓存
    setInterval(() => {
      this.memoryCache.cleanup()
      this.persistentCache.cleanup()
    }, 10 * 60 * 1000) // 10分钟
  }

  // 内存缓存操作
  memory = {
    set: <T>(key: string, data: T, expiry?: number) => 
      this.memoryCache.set(key, data, expiry),
    get: <T>(key: string) => this.memoryCache.get<T>(key),
    delete: (key: string) => this.memoryCache.delete(key),
    clear: () => this.memoryCache.clear()
  }

  // 持久化缓存操作
  persistent = {
    set: <T>(key: string, data: T, expiry?: number) => 
      this.persistentCache.set(key, data, expiry),
    get: <T>(key: string) => this.persistentCache.get<T>(key),
    delete: (key: string) => this.persistentCache.delete(key),
    clear: () => this.persistentCache.clear()
  }

  // LRU缓存操作
  lru = {
    set: <T>(key: string, data: T) => this.lruCache.set(key, data),
    get: <T>(key: string) => this.lruCache.get(key) as T | undefined,
    delete: (key: string) => this.lruCache.delete(key),
    clear: () => this.lruCache.clear()
  }

  // 智能缓存（自动选择合适的缓存策略）
  smart = {
    set: <T>(key: string, data: T, options: {
      strategy?: 'memory' | 'persistent' | 'lru'
      expiry?: number
    } = {}) => {
      const { strategy = 'memory', expiry } = options
      
      switch (strategy) {
        case 'persistent':
          this.persistent.set(key, data, expiry)
          break
        case 'lru':
          this.lru.set(key, data)
          break
        default:
          this.memory.set(key, data, expiry)
      }
    },
    
    get: <T>(key: string): T | null => {
      // 按优先级查找：内存 -> LRU -> 持久化
      return this.memory.get<T>(key) || 
             this.lru.get<T>(key) || 
             this.persistent.get<T>(key)
    }
  }

  // 获取缓存统计信息
  getStats() {
    return {
      memory: {
        size: this.memoryCache.size(),
        keys: this.memoryCache.keys()
      },
      persistent: {
        size: this.persistentCache.size(),
        keys: this.persistentCache.keys()
      },
      lru: {
        size: this.lruCache.size()
      }
    }
  }

  // 清理所有缓存
  clearAll() {
    this.memoryCache.clear()
    this.persistentCache.clear()
    this.lruCache.clear()
  }
}

// 创建全局缓存管理器实例
export const cacheManager = new CacheManager()

// 导出缓存类
export { MemoryCache, PersistentCache, LRUCache, CacheManager }

// 便捷方法
export const cache = cacheManager.smart
export const memoryCache = cacheManager.memory
export const persistentCache = cacheManager.persistent
export const lruCache = cacheManager.lru
