<template>
  <div class="reward-config-form">
    <div class="form-header">
      <h3>奖励配置</h3>
      <div class="tongbao-info">
        <span>总通宝：{{ formatTongbao(totalTongbao) }}</span>
        <span>已分配：{{ formatTongbao(allocatedTongbao) }}</span>
        <span>剩余：{{ formatTongbao(remainingTongbao) }}</span>
      </div>
    </div>
    
    <div class="rewards-list">
      <div 
        v-for="(reward, index) in rewards" 
        :key="index"
        class="reward-item"
      >
        <el-card>
          <template #header>
            <div class="reward-header">
              <span>奖励 {{ index + 1 }}</span>
              <el-button 
                type="danger" 
                size="small" 
                text
                @click="removeReward(index)"
                :disabled="rewards.length <= 1"
              >
                删除
              </el-button>
            </div>
          </template>
          
          <el-form :model="reward" label-width="100px">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="奖励名称" required>
                  <el-input
                    v-model="reward.rewardName"
                    placeholder="请输入奖励名称"
                    maxlength="30"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="奖励类型" required>
                  <el-select 
                    v-model="reward.rewardType" 
                    style="width: 100%"
                    @change="handleRewardTypeChange(reward)"
                  >
                    <el-option label="通宝奖励" value="tongbao" />
                    <el-option label="实物奖励" value="physical" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="通宝数量" required>
                  <el-input-number
                    v-model="reward.tongbaoAmount"
                    :min="0"
                    :max="totalTongbao"
                    style="width: 100%"
                    @change="calculateProbabilities"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="权重" required>
                  <el-input-number
                    v-model="reward.weight"
                    :min="1"
                    :max="10000"
                    style="width: 100%"
                    @change="calculateProbabilities"
                  />
                  <div class="form-tip">
                    概率：{{ reward.probability?.toFixed(2) }}%
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="每日数量" required>
                  <el-input-number
                    v-model="reward.dailyQuantity"
                    :min="1"
                    :max="1000"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12" v-if="reward.rewardType === 'physical'">
                <el-form-item label="实物描述">
                  <el-input
                    v-model="reward.physicalItem"
                    placeholder="请输入实物描述"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    
    <div class="form-actions">
      <el-button @click="addReward" :disabled="rewards.length >= 10">
        <el-icon><Plus /></el-icon>
        添加奖励
      </el-button>
      
      <div class="validation-info">
        <el-alert
          v-if="validationMessage"
          :title="validationMessage"
          :type="isValid ? 'success' : 'warning'"
          show-icon
          :closable="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { formatTongbao, calculateProbability } from '@/utils'
import type { Reward } from '@/types'

const props = defineProps<{
  modelValue: Partial<Reward>[]
  totalTongbao: number
  errors?: Record<string, string>
}>()

const emit = defineEmits<{
  'update:modelValue': [value: Partial<Reward>[]]
  validate: [isValid: boolean]
}>()

const rewards = ref<Partial<Reward>[]>([...props.modelValue])

// 如果没有奖励，添加默认奖励
if (rewards.value.length === 0) {
  rewards.value.push({
    rewardName: '',
    rewardType: 'tongbao',
    tongbaoAmount: 0,
    weight: 100,
    dailyQuantity: 10,
    probability: 0
  })
}

// 计算已分配的通宝
const allocatedTongbao = computed(() => {
  return rewards.value.reduce((total, reward) => {
    return total + (reward.tongbaoAmount || 0) * (reward.dailyQuantity || 0)
  }, 0)
})

// 计算剩余通宝
const remainingTongbao = computed(() => {
  return props.totalTongbao - allocatedTongbao.value
})

// 计算总权重
const totalWeight = computed(() => {
  return rewards.value.reduce((total, reward) => total + (reward.weight || 0), 0)
})

// 验证状态
const isValid = computed(() => {
  // 检查是否有空的必填字段
  const hasEmptyFields = rewards.value.some(reward => 
    !reward.rewardName || 
    !reward.rewardType || 
    !reward.tongbaoAmount || 
    !reward.weight || 
    !reward.dailyQuantity
  )
  
  // 检查通宝分配是否合理
  const tongbaoValid = allocatedTongbao.value <= props.totalTongbao && allocatedTongbao.value > 0
  
  return !hasEmptyFields && tongbaoValid && rewards.value.length > 0
})

// 验证消息
const validationMessage = computed(() => {
  if (rewards.value.length === 0) {
    return '请至少添加一个奖励'
  }
  
  if (allocatedTongbao.value > props.totalTongbao) {
    return '奖励通宝总数超出了活动总通宝数量'
  }
  
  if (allocatedTongbao.value === 0) {
    return '请设置奖励通宝数量'
  }
  
  const hasEmptyFields = rewards.value.some(reward => 
    !reward.rewardName || !reward.rewardType || !reward.tongbaoAmount || !reward.weight || !reward.dailyQuantity
  )
  
  if (hasEmptyFields) {
    return '请完善所有奖励的必填信息'
  }
  
  return '奖励配置完成'
})

// 添加奖励
const addReward = () => {
  if (rewards.value.length < 10) {
    rewards.value.push({
      rewardName: '',
      rewardType: 'tongbao',
      tongbaoAmount: 0,
      weight: 100,
      dailyQuantity: 10,
      probability: 0
    })
    calculateProbabilities()
  }
}

// 删除奖励
const removeReward = (index: number) => {
  if (rewards.value.length > 1) {
    rewards.value.splice(index, 1)
    calculateProbabilities()
  }
}

// 处理奖励类型变化
const handleRewardTypeChange = (reward: Partial<Reward>) => {
  if (reward.rewardType === 'tongbao') {
    reward.physicalItem = undefined
  }
}

// 计算概率
const calculateProbabilities = () => {
  const total = totalWeight.value
  rewards.value.forEach(reward => {
    reward.probability = calculateProbability(reward.weight || 0, total)
  })
}

// 监听数据变化
watch(rewards, (newValue) => {
  emit('update:modelValue', [...newValue])
}, { deep: true })

watch(isValid, (valid) => {
  emit('validate', valid)
})

// 初始化概率计算
nextTick(() => {
  calculateProbabilities()
  emit('validate', isValid.value)
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.reward-config-form {
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;
    
    h3 {
      margin: 0;
      color: $text-color-primary;
    }
    
    .tongbao-info {
      display: flex;
      gap: $spacing-md;
      font-size: $font-size-sm;
      
      span {
        padding: $spacing-xs $spacing-sm;
        background: $background-color-light;
        border-radius: $border-radius-sm;
        
        &:last-child {
          color: $primary-color;
          font-weight: 600;
        }
      }
    }
  }
  
  .rewards-list {
    .reward-item {
      margin-bottom: $spacing-lg;
      
      .reward-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
      }
      
      .form-tip {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        margin-top: $spacing-xs;
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-xl;
    
    .validation-info {
      flex: 1;
      margin-left: $spacing-lg;
    }
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .reward-config-form {
    .form-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;
      
      .tongbao-info {
        flex-direction: column;
        gap: $spacing-xs;
      }
    }
    
    .form-actions {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-md;
      
      .validation-info {
        margin-left: 0;
      }
    }
  }
}
</style>
