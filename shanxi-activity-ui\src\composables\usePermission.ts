import { computed } from 'vue'
import { useUserStore } from '@/stores'
import type { UserRole } from '@/types'

// 权限定义
export interface Permission {
  // 活动管理权限
  canCreateActivity: boolean
  canEditActivity: boolean
  canDeleteActivity: boolean
  canCloseActivity: boolean
  canViewActivityStatistics: boolean
  canExportActivityData: boolean
  
  // 玩家参与权限
  canParticipateActivity: boolean
  canDrawReward: boolean
  canViewOwnRecords: boolean
  canViewAllRecords: boolean
  
  // 系统管理权限
  canManageUsers: boolean
  canViewSystemLogs: boolean
  canConfigureSystem: boolean
  
  // 数据访问权限
  canViewSensitiveData: boolean
  canExportUserData: boolean
}

// 角色权限映射
const ROLE_PERMISSIONS: Record<UserRole, Permission> = {
  alliance_leader: {
    // 盟主拥有所有权限
    canCreateActivity: true,
    canEditActivity: true,
    canDeleteActivity: true,
    canCloseActivity: true,
    canViewActivityStatistics: true,
    canExportActivityData: true,
    canParticipateActivity: true,
    canDrawReward: true,
    canViewOwnRecords: true,
    canViewAllRecords: true,
    canManageUsers: true,
    canViewSystemLogs: true,
    canConfigureSystem: true,
    canViewSensitiveData: true,
    canExportUserData: true
  },
  
  partner: {
    // 合作伙伴拥有部分管理权限
    canCreateActivity: true,
    canEditActivity: true,
    canDeleteActivity: false,
    canCloseActivity: true,
    canViewActivityStatistics: true,
    canExportActivityData: true,
    canParticipateActivity: true,
    canDrawReward: true,
    canViewOwnRecords: true,
    canViewAllRecords: true,
    canManageUsers: false,
    canViewSystemLogs: false,
    canConfigureSystem: false,
    canViewSensitiveData: false,
    canExportUserData: false
  },
  
  player: {
    // 玩家只有参与权限
    canCreateActivity: false,
    canEditActivity: false,
    canDeleteActivity: false,
    canCloseActivity: false,
    canViewActivityStatistics: false,
    canExportActivityData: false,
    canParticipateActivity: true,
    canDrawReward: true,
    canViewOwnRecords: true,
    canViewAllRecords: false,
    canManageUsers: false,
    canViewSystemLogs: false,
    canConfigureSystem: false,
    canViewSensitiveData: false,
    canExportUserData: false
  }
}

// 页面访问权限
export interface PagePermission {
  path: string
  roles: UserRole[]
  requiresAuth: boolean
}

export const PAGE_PERMISSIONS: PagePermission[] = [
  // 活动管理页面
  {
    path: '/activity/create',
    roles: ['alliance_leader', 'partner'],
    requiresAuth: true
  },
  {
    path: '/activity/list',
    roles: ['alliance_leader', 'partner'],
    requiresAuth: true
  },
  {
    path: '/activity/history',
    roles: ['alliance_leader', 'partner'],
    requiresAuth: true
  },
  {
    path: '/activity/:id/statistics',
    roles: ['alliance_leader', 'partner'],
    requiresAuth: true
  },
  
  // 玩家页面
  {
    path: '/player/activities',
    roles: ['player'],
    requiresAuth: true
  },
  {
    path: '/player/activity/:id',
    roles: ['player'],
    requiresAuth: true
  },
  {
    path: '/player/records',
    roles: ['player'],
    requiresAuth: true
  },
  
  // 系统管理页面
  {
    path: '/system/users',
    roles: ['alliance_leader'],
    requiresAuth: true
  },
  {
    path: '/system/logs',
    roles: ['alliance_leader'],
    requiresAuth: true
  }
]

export function usePermission() {
  const userStore = useUserStore()
  
  // 当前用户权限
  const permissions = computed<Permission>(() => {
    const userRole = userStore.userInfo?.role
    if (!userRole) {
      // 未登录用户没有任何权限
      return Object.keys(ROLE_PERMISSIONS.player).reduce((acc, key) => {
        acc[key as keyof Permission] = false
        return acc
      }, {} as Permission)
    }
    
    return ROLE_PERMISSIONS[userRole]
  })
  
  // 检查是否有特定权限
  const hasPermission = (permission: keyof Permission): boolean => {
    return permissions.value[permission]
  }
  
  // 检查是否有任一权限
  const hasAnyPermission = (permissionList: (keyof Permission)[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }
  
  // 检查是否有所有权限
  const hasAllPermissions = (permissionList: (keyof Permission)[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }
  
  // 检查页面访问权限
  const canAccessPage = (path: string): boolean => {
    const pagePermission = PAGE_PERMISSIONS.find(p => {
      // 支持动态路由匹配
      const regex = new RegExp('^' + p.path.replace(/:\w+/g, '[^/]+') + '$')
      return regex.test(path)
    })
    
    if (!pagePermission) {
      // 没有配置权限的页面默认允许访问
      return true
    }
    
    if (pagePermission.requiresAuth && !userStore.isLoggedIn) {
      return false
    }
    
    const userRole = userStore.userInfo?.role
    if (!userRole) {
      return false
    }
    
    return pagePermission.roles.includes(userRole)
  }
  
  // 检查用户角色
  const hasRole = (role: UserRole): boolean => {
    return userStore.userInfo?.role === role
  }
  
  // 检查是否有任一角色
  const hasAnyRole = (roles: UserRole[]): boolean => {
    const userRole = userStore.userInfo?.role
    return userRole ? roles.includes(userRole) : false
  }
  
  // 获取用户可访问的菜单项
  const getAccessibleMenuItems = () => {
    const userRole = userStore.userInfo?.role
    if (!userRole) return []
    
    const menuItems = [
      {
        path: '/activity',
        name: '活动管理',
        roles: ['alliance_leader', 'partner'],
        children: [
          { path: '/activity/create', name: '创建活动', roles: ['alliance_leader', 'partner'] },
          { path: '/activity/list', name: '活动列表', roles: ['alliance_leader', 'partner'] },
          { path: '/activity/history', name: '历史活动', roles: ['alliance_leader', 'partner'] }
        ]
      },
      {
        path: '/player',
        name: '我的活动',
        roles: ['player'],
        children: [
          { path: '/player/activities', name: '参与活动', roles: ['player'] },
          { path: '/player/records', name: '中奖记录', roles: ['player'] }
        ]
      },
      {
        path: '/system',
        name: '系统管理',
        roles: ['alliance_leader'],
        children: [
          { path: '/system/users', name: '用户管理', roles: ['alliance_leader'] },
          { path: '/system/logs', name: '系统日志', roles: ['alliance_leader'] }
        ]
      }
    ]
    
    return menuItems.filter(item => 
      item.roles.includes(userRole)
    ).map(item => ({
      ...item,
      children: item.children?.filter(child => child.roles.includes(userRole))
    }))
  }
  
  // 权限守卫函数
  const requirePermission = (permission: keyof Permission, errorMessage?: string) => {
    if (!hasPermission(permission)) {
      throw new Error(errorMessage || `需要 ${permission} 权限`)
    }
  }
  
  const requireRole = (role: UserRole, errorMessage?: string) => {
    if (!hasRole(role)) {
      throw new Error(errorMessage || `需要 ${role} 角色`)
    }
  }
  
  const requireAnyRole = (roles: UserRole[], errorMessage?: string) => {
    if (!hasAnyRole(roles)) {
      throw new Error(errorMessage || `需要以下角色之一: ${roles.join(', ')}`)
    }
  }
  
  return {
    // 权限对象
    permissions,
    
    // 权限检查方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessPage,
    
    // 角色检查方法
    hasRole,
    hasAnyRole,
    
    // 便捷属性
    isAllianceLeader: computed(() => hasRole('alliance_leader')),
    isPartner: computed(() => hasRole('partner')),
    isPlayer: computed(() => hasRole('player')),
    canManageActivity: computed(() => hasAnyRole(['alliance_leader', 'partner'])),
    
    // 菜单和导航
    getAccessibleMenuItems,
    
    // 权限守卫
    requirePermission,
    requireRole,
    requireAnyRole
  }
}
