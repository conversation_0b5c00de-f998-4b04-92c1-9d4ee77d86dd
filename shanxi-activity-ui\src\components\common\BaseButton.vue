<template>
  <el-button
    :type="type"
    :size="size"
    :disabled="disabled"
    :loading="loading"
    :icon="icon"
    :class="[
      'base-button',
      {
        'is-gradient': gradient,
        'is-round': round,
        'is-block': block
      }
    ]"
    @click="handleClick"
  >
    <slot />
  </el-button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default'
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  loading?: boolean
  icon?: any
  gradient?: boolean
  round?: boolean
  block?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'default',
  disabled: false,
  loading: false,
  gradient: false,
  round: false,
  block: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.base-button {
  &.is-gradient {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    border: none;
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, darken($primary-color, 5%) 0%, $primary-color 100%);
    }
    
    &:active {
      background: linear-gradient(135deg, darken($primary-color, 10%) 0%, darken($primary-color, 5%) 100%);
    }
  }
  
  &.is-round {
    border-radius: 20px;
  }
  
  &.is-block {
    width: 100%;
    display: block;
  }
}
</style>
