import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { UserInfo, UserRole } from '@/types'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const tongbaoBalance = ref(0)
  const isLoggedIn = ref(false)

  // 计算属性
  const canManageActivity = computed(() => {
    if (!userInfo.value) return false
    return ['alliance_leader', 'partner'].includes(userInfo.value.role)
  })

  const isPlayer = computed(() => {
    return userInfo.value?.role === 'player'
  })

  const isAllianceLeader = computed(() => {
    return userInfo.value?.role === 'alliance_leader'
  })

  const isPartner = computed(() => {
    return userInfo.value?.role === 'partner'
  })

  // 方法
  const fetchUserInfo = async () => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟用户数据
      userInfo.value = {
        id: 1,
        gameUserId: 'user123',
        nickname: '测试用户',
        role: 'alliance_leader' as UserRole,
        avatar: '',
        tongbaoBalance: 5000,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      tongbaoBalance.value = userInfo.value.tongbaoBalance
      isLoggedIn.value = true
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      throw error
    }
  }

  const updateTongbaoBalance = (amount: number) => {
    tongbaoBalance.value = amount
    if (userInfo.value) {
      userInfo.value.tongbaoBalance = amount
    }
  }

  const logout = () => {
    userInfo.value = null
    tongbaoBalance.value = 0
    isLoggedIn.value = false
  }

  return {
    // 状态
    userInfo,
    tongbaoBalance,
    isLoggedIn,
    
    // 计算属性
    canManageActivity,
    isPlayer,
    isAllianceLeader,
    isPartner,
    
    // 方法
    fetchUserInfo,
    updateTongbaoBalance,
    logout
  }
})
