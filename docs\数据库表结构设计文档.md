# 陕西平台活动开放工具 - 数据库表结构设计文档

## 1. 概述

本文档描述了陕西平台活动开放工具的完整数据库表结构设计，包括用户管理、活动管理、任务系统、抽奖系统、奖励管理等核心功能模块。

## 2. 数据库设计原则

- **数据一致性**：通过外键约束保证数据完整性
- **性能优化**：合理设置索引，优化查询性能
- **扩展性**：预留扩展字段，支持未来功能扩展
- **安全性**：敏感数据加密存储，操作日志完整记录

## 3. 表结构设计

### 3.1 用户信息缓存模块

#### 3.1.1 用户信息缓存表 (user_cache)
仅缓存参与活动的用户基本信息，用于显示昵称等。用户权限验证由游戏系统负责。

```sql
CREATE TABLE user_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '缓存记录ID',
    game_user_id VARCHAR(50) NOT NULL UNIQUE COMMENT '游戏系统用户ID',
    nickname VARCHAR(100) NOT NULL COMMENT '昵称',
    last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_game_user_id (game_user_id),
    INDEX idx_last_update (last_update_time)
) COMMENT='用户信息缓存表（仅用于显示，不存储权限信息）';
```

### 3.2 活动管理模块

#### 3.2.1 活动表 (activities)
存储活动的基本信息和配置。

```sql
CREATE TABLE activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动ID',
    creator_game_user_id VARCHAR(50) NOT NULL COMMENT '创建者游戏用户ID',
    creator_nickname VARCHAR(100) NOT NULL COMMENT '创建者昵称',
    activity_name VARCHAR(200) NOT NULL COMMENT '活动名称',
    activity_type ENUM('blind_box') NOT NULL DEFAULT 'blind_box' COMMENT '活动类型',
    total_tongbao DECIMAL(15,2) NOT NULL COMMENT '投入的总通宝',
    remaining_tongbao DECIMAL(15,2) NOT NULL COMMENT '剩余通宝',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    status ENUM('not_started', 'running', 'ended', 'closed') NOT NULL DEFAULT 'not_started' COMMENT '活动状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_creator (creator_game_user_id),
    INDEX idx_status (status),
    INDEX idx_time (start_time, end_time)
) COMMENT='活动基本信息表';
```



### 3.3 奖励管理模块

#### 3.3.1 奖励配置表 (activity_rewards)
存储活动中的奖品配置信息。

```sql
CREATE TABLE activity_rewards (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '奖励ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    reward_name VARCHAR(200) NOT NULL COMMENT '奖励名称',
    reward_type ENUM('tongbao', 'physical') NOT NULL COMMENT '奖励类型：通宝/实物',
    tongbao_amount DECIMAL(15,2) NOT NULL DEFAULT 0 COMMENT '通宝数量',
    physical_item VARCHAR(500) NULL COMMENT '实物描述',
    weight INT NOT NULL COMMENT '权重',
    probability DECIMAL(8,6) NOT NULL COMMENT '中奖概率',
    daily_quantity INT NOT NULL COMMENT '每日份数',
    remaining_quantity INT NOT NULL COMMENT '剩余份数',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_activity (activity_id),
    INDEX idx_probability (probability),
    INDEX idx_sort (sort_order),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
) COMMENT='活动奖励配置表';
```

### 3.4 任务系统模块

#### 3.4.1 任务配置表 (activity_tasks)
存储活动中的任务配置。

```sql
CREATE TABLE activity_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    task_type ENUM('login', 'game_rounds', 'service_fee') NOT NULL COMMENT '任务类型',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    target_value INT NOT NULL COMMENT '目标数值',
    reward_chances INT NOT NULL COMMENT '奖励次数',
    refresh_type ENUM('daily', 'weekly', 'never') NOT NULL COMMENT '刷新类型',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_activity (activity_id),
    INDEX idx_task_type (task_type),
    INDEX idx_sort (sort_order),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
) COMMENT='活动任务配置表';
```

#### 3.4.2 玩家任务进度表 (player_task_progress)
记录玩家的任务完成进度。

```sql
CREATE TABLE player_task_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '进度ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    player_game_user_id VARCHAR(50) NOT NULL COMMENT '玩家游戏用户ID',
    player_nickname VARCHAR(100) NOT NULL COMMENT '玩家昵称',
    current_progress INT NOT NULL DEFAULT 0 COMMENT '当前进度',
    completed_times INT NOT NULL DEFAULT 0 COMMENT '完成次数',
    last_refresh_time TIMESTAMP NULL COMMENT '上次刷新时间',
    last_completed_time TIMESTAMP NULL COMMENT '上次完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_activity_task_player (activity_id, task_id, player_game_user_id),
    INDEX idx_player (player_game_user_id),
    INDEX idx_activity (activity_id),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES activity_tasks(id) ON DELETE CASCADE
) COMMENT='玩家任务进度表';
```

### 3.5 抽奖系统模块

#### 3.5.1 玩家抽奖次数表 (player_draw_chances)
管理玩家的抽奖次数。

```sql
CREATE TABLE player_draw_chances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    player_game_user_id VARCHAR(50) NOT NULL COMMENT '玩家游戏用户ID',
    player_nickname VARCHAR(100) NOT NULL COMMENT '玩家昵称',
    total_chances INT NOT NULL DEFAULT 0 COMMENT '总次数',
    used_chances INT NOT NULL DEFAULT 0 COMMENT '已使用次数',
    remaining_chances INT NOT NULL DEFAULT 0 COMMENT '剩余次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_activity_player (activity_id, player_game_user_id),
    INDEX idx_player (player_game_user_id),
    INDEX idx_activity (activity_id),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
) COMMENT='玩家抽奖次数表';
```

#### 3.5.2 抽奖记录表 (draw_records)
记录所有的抽奖行为。

```sql
CREATE TABLE draw_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '抽奖记录ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    player_game_user_id VARCHAR(50) NOT NULL COMMENT '玩家游戏用户ID',
    player_nickname VARCHAR(100) NOT NULL COMMENT '玩家昵称',
    reward_id BIGINT NOT NULL COMMENT '中奖奖励ID',
    draw_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '抽奖时间',

    INDEX idx_activity (activity_id),
    INDEX idx_player (player_game_user_id),
    INDEX idx_draw_time (draw_time),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
    FOREIGN KEY (reward_id) REFERENCES activity_rewards(id) ON DELETE RESTRICT
) COMMENT='抽奖记录表';
```

#### 3.5.3 中奖记录表 (prize_records)
记录中奖结果和奖励发放状态。

```sql
CREATE TABLE prize_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '中奖记录ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    player_game_user_id VARCHAR(50) NOT NULL COMMENT '玩家游戏用户ID',
    player_nickname VARCHAR(100) NOT NULL COMMENT '玩家昵称',
    reward_id BIGINT NOT NULL COMMENT '奖励ID',
    reward_name VARCHAR(200) NOT NULL COMMENT '奖励名称',
    reward_type ENUM('tongbao', 'physical') NOT NULL COMMENT '奖励类型',
    tongbao_amount DECIMAL(15,2) NOT NULL DEFAULT 0 COMMENT '通宝数量',
    physical_item VARCHAR(500) NULL COMMENT '实物描述',
    status ENUM('pending', 'distributed', 'failed') NOT NULL DEFAULT 'pending' COMMENT '发放状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_activity (activity_id),
    INDEX idx_player (player_game_user_id),
    INDEX idx_status (status),
    INDEX idx_created_time (created_at),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE,
    FOREIGN KEY (reward_id) REFERENCES activity_rewards(id) ON DELETE RESTRICT
) COMMENT='中奖记录表';
```

### 3.6 系统功能模块

#### 3.6.1 通宝流水表 (tongbao_transactions)
记录活动相关的通宝收支流水。

```sql
CREATE TABLE tongbao_transactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '流水ID',
    game_user_id VARCHAR(50) NOT NULL COMMENT '游戏用户ID',
    user_nickname VARCHAR(100) NOT NULL COMMENT '用户昵称',
    activity_id BIGINT NULL COMMENT '关联活动ID',
    transaction_type ENUM('activity_invest', 'prize_reward', 'system_adjust') NOT NULL COMMENT '交易类型',
    amount DECIMAL(15,2) NOT NULL COMMENT '金额（正数为收入，负数为支出）',
    balance_before DECIMAL(15,2) NOT NULL COMMENT '交易前余额',
    balance_after DECIMAL(15,2) NOT NULL COMMENT '交易后余额',
    description VARCHAR(500) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_game_user_id (game_user_id),
    INDEX idx_activity (activity_id),
    INDEX idx_type (transaction_type),
    INDEX idx_created_time (created_at),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE SET NULL
) COMMENT='通宝流水记录表（仅记录活动相关流水）';
```

#### 3.6.2 跑马灯消息表 (marquee_messages)
存储跑马灯消息。

```sql
CREATE TABLE marquee_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    player_game_user_id VARCHAR(50) NOT NULL COMMENT '玩家游戏用户ID',
    player_nickname VARCHAR(100) NOT NULL COMMENT '玩家昵称',
    reward_name VARCHAR(200) NOT NULL COMMENT '奖励名称',
    message_content TEXT NOT NULL COMMENT '消息内容',
    play_count INT NOT NULL DEFAULT 0 COMMENT '播放次数',
    last_played_at TIMESTAMP NULL COMMENT '最后播放时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_activity (activity_id),
    INDEX idx_created_time (created_at),
    INDEX idx_play_count (play_count),
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
) COMMENT='跑马灯消息表';
```

#### 3.6.3 系统配置表 (system_config)
存储系统级配置参数。

```sql
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key)
) COMMENT='系统配置表';
```

## 4. 数据库初始化脚本

### 4.1 创建数据库
```sql
CREATE DATABASE shanxi_activity_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE shanxi_activity_platform;
```

### 4.2 初始化系统配置
```sql
INSERT INTO system_config (config_key, config_value, description) VALUES
('marquee_display_duration', '5', '跑马灯消息显示时长（秒）'),
('marquee_cycle_interval', '30', '跑马灯循环播放间隔（秒）'),
('task_refresh_time', '00:00:00', '任务刷新时间'),
('max_draw_records_display', '50', '最大显示抽奖记录数'),
('probability_precision', '6', '概率计算精度（小数位数）'),
('user_sync_interval', '300', '用户数据同步间隔（秒）'),
('game_system_api_url', 'https://game.example.com/api', '游戏系统API地址');
```

## 5. 索引优化建议

### 5.1 复合索引
- `activities` 表：`(creator_id, status, start_time)`
- `prize_records` 表：`(activity_id, player_id, created_at)`
- `player_task_progress` 表：`(player_id, activity_id, last_refresh_time)`

### 5.2 分区建议
对于数据量较大的表，建议按时间分区：
- `draw_records` 按月分区
- `prize_records` 按月分区
- `tongbao_transactions` 按月分区

## 6. 数据备份策略

### 6.1 备份频率
- 全量备份：每日凌晨2点
- 增量备份：每4小时一次
- 日志备份：实时备份

### 6.2 数据保留策略
- 活动数据：永久保留
- 抽奖记录：保留2年
- 系统日志：保留6个月

---

**文档版本**: v1.0  
**最后更新**: 2025-07-31  
**维护人员**: 开发团队
