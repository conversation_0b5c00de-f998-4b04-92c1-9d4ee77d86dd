<template>
  <el-dialog v-model="visible" title="奖品详情" width="500px" class="reward-detail-dialog">
    <div class="reward-detail" v-if="reward">
      <!-- 奖品展示 -->
      <div class="reward-display">
        <div class="reward-icon">
          <el-icon size="64" :class="iconClass">
            <component :is="rewardIcon" />
          </el-icon>
        </div>

        <div class="reward-info">
          <h3 class="reward-name">{{ reward.rewardName }}</h3>

          <div class="reward-type">
            <StatusTag :status="reward.rewardType === 'tongbao' ? 'success' : 'warning'">
              {{ reward.rewardType === 'tongbao' ? '通宝奖励' : '实物奖励' }}
            </StatusTag>
          </div>

          <div class="reward-value" v-if="reward.rewardType === 'tongbao'">
            {{ formatTongbao(reward.tongbaoAmount) }}
          </div>

          <div class="reward-description" v-if="reward.physicalItem">
            {{ reward.physicalItem }}
          </div>
        </div>

        <!-- 稀有度展示 -->
        <div class="rarity-display" v-if="rarityLevel > 0">
          <div class="rarity-stars">
            <el-icon v-for="i in 5" :key="i" :class="{ 'is-active': i <= rarityLevel }" class="star-icon">
              <Star />
            </el-icon>
          </div>
          <div class="rarity-text">{{ getRarityText() }}</div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="detail-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="中奖概率">
            <span class="probability-value">
              {{ reward.probability?.toFixed(2) }}%
            </span>
            <el-tag :type="getProbabilityType()" size="small" class="probability-tag">
              {{ getProbabilityText() }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="每日数量">
            {{ reward.dailyQuantity }}
          </el-descriptions-item>

          <el-descriptions-item label="剩余数量" v-if="reward.remainingQuantity !== undefined">
            <span :class="{ 'low-stock': isLowStock }">
              {{ reward.remainingQuantity }}
            </span>
            <el-tag v-if="isLowStock" type="danger" size="small">
              库存不足
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="权重值">
            {{ reward.weight }}
          </el-descriptions-item>

          <el-descriptions-item label="奖品ID" v-if="reward.id">
            {{ reward.id }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 概率说明 -->
      <div class="probability-explanation">
        <el-alert title="概率说明" type="info" :closable="false" show-icon>
          <template #default>
            <div class="explanation-content">
              <p>• 中奖概率基于权重计算，实际概率可能有所差异</p>
              <p>• 每日奖品数量有限，先到先得</p>
              <p>• 概率越低的奖品越珍贵</p>
              <p v-if="rarityLevel >= 4">• 这是一个{{ getRarityText() }}奖品！</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 历史中奖记录 -->
      <div class="recent-winners" v-if="showRecentWinners">
        <h4>最近中奖</h4>
        <div class="winners-list">
          <div v-for="winner in recentWinners" :key="winner.id" class="winner-item">
            <el-avatar :size="24">{{ winner.playerNickname.charAt(0) }}</el-avatar>
            <span class="winner-name">{{ winner.playerNickname }}</span>
            <span class="winner-time">{{ getRelativeTime(winner.drawTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleWantThis">
          我想要这个！
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Star, Coin, Present, Trophy } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { StatusTag } from '@/components/common'
import { formatTongbao } from '@/utils'
import type { Reward, DrawRecord } from '@/types'

interface Props {
  modelValue: boolean
  reward: Reward | null
  showRecentWinners?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showRecentWinners: true
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const recentWinners = ref<DrawRecord[]>([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const rewardIcon = computed(() => {
  if (!props.reward) return Present

  if (props.reward.rewardType === 'tongbao') {
    return Coin
  } else if (rarityLevel.value >= 4) {
    return Trophy
  } else {
    return Present
  }
})

const iconClass = computed(() => [
  'reward-icon-component',
  {
    'icon-tongbao': props.reward?.rewardType === 'tongbao',
    'icon-physical': props.reward?.rewardType === 'physical',
    'icon-rare': rarityLevel.value >= 3,
    'icon-epic': rarityLevel.value >= 4,
    'icon-legendary': rarityLevel.value >= 5
  }
])

const rarityLevel = computed(() => {
  if (!props.reward?.probability) return 0

  const prob = props.reward.probability
  if (prob <= 1) return 5 // 传说
  if (prob <= 5) return 4 // 史诗
  if (prob <= 15) return 3 // 稀有
  if (prob <= 30) return 2 // 不常见
  return 1 // 普通
})

const isLowStock = computed(() => {
  return props.reward?.remainingQuantity !== undefined &&
    props.reward.remainingQuantity < 5
})

// 方法
const getRarityText = () => {
  const rarityMap: Record<number, string> = {
    5: '传说',
    4: '史诗',
    3: '稀有',
    2: '不常见',
    1: '普通'
  }
  return rarityMap[rarityLevel.value] || ''
}

const getProbabilityType = () => {
  if (!props.reward?.probability) return 'info'

  const prob = props.reward.probability
  if (prob <= 1) return 'danger'
  if (prob <= 5) return 'warning'
  if (prob <= 15) return 'primary'
  return 'success'
}

const getProbabilityText = () => {
  if (!props.reward?.probability) return ''

  const prob = props.reward.probability
  if (prob <= 1) return '极难'
  if (prob <= 5) return '很难'
  if (prob <= 15) return '较难'
  if (prob <= 30) return '一般'
  return '容易'
}

const getRelativeTime = (dateTime: string) => {
  const now = Date.now()
  const time = new Date(dateTime).getTime()
  const diff = now - time

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return '很久以前'
}

const handleWantThis = () => {
  ElMessage.success('已记录您的心愿，祝您好运！')
  visible.value = false
}

// 监听弹窗打开，加载最近中奖记录
watch(() => props.modelValue, (isOpen) => {
  if (isOpen && props.reward && props.showRecentWinners) {
    loadRecentWinners()
  }
})

const loadRecentWinners = async () => {
  try {
    // 模拟加载最近中奖记录
    await new Promise(resolve => setTimeout(resolve, 500))

    recentWinners.value = [
      {
        id: 1,
        playerNickname: '幸运玩家A',
        drawTime: new Date(Date.now() - 1800000).toISOString() // 30分钟前
      },
      {
        id: 2,
        playerNickname: '幸运玩家B',
        drawTime: new Date(Date.now() - 7200000).toISOString() // 2小时前
      }
    ] as DrawRecord[]
  } catch (error) {
    console.error('Failed to load recent winners:', error)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.reward-detail-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-lg;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    color: white;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }
  }

  .reward-detail {
    .reward-display {
      text-align: center;
      margin-bottom: $spacing-xl;
      padding: $spacing-lg;
      background: $background-color-light;
      border-radius: $border-radius-base;

      .reward-icon {
        margin-bottom: $spacing-md;

        .reward-icon-component {
          &.icon-tongbao {
            color: $success-color;
          }

          &.icon-physical {
            color: $warning-color;
          }

          &.icon-rare {
            color: $warning-color;
            filter: drop-shadow(0 0 12px rgba($warning-color, 0.5));
          }

          &.icon-epic {
            color: #9C27B0;
            filter: drop-shadow(0 0 16px rgba(#9C27B0, 0.6));
          }

          &.icon-legendary {
            color: #FF5722;
            filter: drop-shadow(0 0 20px rgba(#FF5722, 0.7));
            animation: legendary-pulse 2s ease-in-out infinite;
          }
        }
      }

      .reward-info {
        margin-bottom: $spacing-md;

        .reward-name {
          margin: 0 0 $spacing-md 0;
          color: $text-color-primary;
          font-size: $font-size-xl;
        }

        .reward-type {
          margin-bottom: $spacing-md;
        }

        .reward-value {
          font-size: $font-size-xxl;
          font-weight: 700;
          color: $success-color;
          margin-bottom: $spacing-sm;
        }

        .reward-description {
          color: $text-color-secondary;
          font-size: $font-size-sm;
        }
      }

      .rarity-display {
        .rarity-stars {
          display: flex;
          justify-content: center;
          gap: 4px;
          margin-bottom: $spacing-xs;

          .star-icon {
            font-size: 18px;
            color: $border-color-light;
            transition: all 0.3s ease;

            &.is-active {
              color: #FFD700;
              filter: drop-shadow(0 0 6px rgba(#FFD700, 0.8));
            }
          }
        }

        .rarity-text {
          font-size: $font-size-base;
          font-weight: 600;
          color: $text-color-primary;
        }
      }
    }

    .detail-info {
      margin-bottom: $spacing-lg;

      .probability-value {
        font-weight: 600;
        margin-right: $spacing-sm;
      }

      .probability-tag {
        margin-left: $spacing-sm;
      }

      .low-stock {
        color: $error-color;
        font-weight: 600;
      }
    }

    .probability-explanation {
      margin-bottom: $spacing-lg;

      .explanation-content {
        p {
          margin: $spacing-xs 0;
          font-size: $font-size-sm;
        }
      }
    }

    .recent-winners {
      h4 {
        margin: 0 0 $spacing-md 0;
        color: $text-color-primary;
      }

      .winners-list {
        .winner-item {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          padding: $spacing-sm 0;
          border-bottom: 1px solid $border-color-light;

          &:last-child {
            border-bottom: none;
          }

          .winner-name {
            flex: 1;
            font-size: $font-size-sm;
            color: $text-color-primary;
          }

          .winner-time {
            font-size: $font-size-sm;
            color: $text-color-secondary;
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: center;
  }
}

@keyframes legendary-pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .reward-detail-dialog {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 5vh auto;
    }
  }
}
</style>
