import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/HomeView.vue'),
    meta: { title: '首页' }
  },
  // 活动管理路由
  {
    path: '/activity',
    name: 'Activity',
    children: [
      {
        path: 'create',
        name: 'ActivityCreate',
        component: () => import('@/views/activity/CreateActivity.vue'),
        meta: { title: '创建活动', requiresAuth: true, roles: ['alliance_leader', 'partner'] }
      },
      {
        path: 'list',
        name: 'ActivityList',
        component: () => import('@/views/activity/ActivityList.vue'),
        meta: { title: '活动列表', requiresAuth: true, roles: ['alliance_leader', 'partner'] }
      },
      {
        path: 'history',
        name: 'ActivityHistory',
        component: () => import('@/views/activity/ActivityHistory.vue'),
        meta: { title: '历史活动', requiresAuth: true, roles: ['alliance_leader', 'partner'] }
      },
      {
        path: ':id',
        name: 'ActivityDetail',
        component: () => import('@/views/activity/ActivityDetail.vue'),
        meta: { title: '活动详情', requiresAuth: true }
      }
    ]
  },
  // 玩家活动路由
  {
    path: '/player',
    name: 'Player',
    children: [
      {
        path: 'activities',
        name: 'PlayerActivities',
        component: () => import('@/views/HomeView.vue'),
        meta: { title: '我的活动', requiresAuth: true, roles: ['player'] }
      },
      {
        path: 'activity/:id',
        name: 'PlayerActivityDetail',
        component: () => import('@/views/player/ActivityParticipation.vue'),
        meta: { title: '参与活动', requiresAuth: true, roles: ['player'] }
      },
      {
        path: 'records',
        name: 'PlayerRecords',
        component: () => import('@/views/HomeView.vue'),
        meta: { title: '中奖记录', requiresAuth: true, roles: ['player'] }
      }
    ]
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/HomeView.vue'),
    meta: { title: '系统管理' }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/HomeView.vue'),
    meta: { title: '数据统计' }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
// 设置路由守卫
setupRouterGuards(router)

export default router
