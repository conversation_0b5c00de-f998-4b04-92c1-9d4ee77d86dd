import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/HomeView.vue'),
    meta: { title: '首页' }
  },
  // 活动管理路由
  {
    path: '/activity',
    name: 'Activity',
    children: [
      {
        path: 'create',
        name: 'ActivityCreate',
        component: () => import('@/views/activity/CreateActivity.vue'),
        meta: { title: '创建活动', requiresAuth: true, roles: ['alliance_leader', 'partner'] }
      },
      {
        path: 'list',
        name: 'ActivityList',
        component: () => import('@/views/activity/ActivityList.vue'),
        meta: { title: '活动列表', requiresAuth: true, roles: ['alliance_leader', 'partner'] }
      },
      {
        path: 'history',
        name: 'ActivityHistory',
        component: () => import('@/views/activity/ActivityHistory.vue'),
        meta: { title: '历史活动', requiresAuth: true, roles: ['alliance_leader', 'partner'] }
      },
      {
        path: ':id',
        name: 'ActivityDetail',
        component: () => import('@/views/activity/ActivityDetail.vue'),
        meta: { title: '活动详情', requiresAuth: true }
      }
    ]
  },
  {
    path: '/player',
    name: 'Player',
    component: () => import('@/views/HomeView.vue'),
    meta: { title: '玩家中心' }
  },
  {
    path: '/admin',
    name: 'Admin',
    component: () => import('@/views/HomeView.vue'),
    meta: { title: '系统管理' }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: () => import('@/views/HomeView.vue'),
    meta: { title: '数据统计' }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 陕西平台活动工具`
  }

  // 这里可以添加权限验证逻辑
  // 暂时允许所有路由访问
  next()
})

export default router
