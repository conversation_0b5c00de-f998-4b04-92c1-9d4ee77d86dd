<template>
  <div class="basic-info-form">
    <el-form 
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      @validate="handleValidate"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="活动名称" prop="activityName">
            <el-input
              v-model="formData.activityName"
              placeholder="请输入活动名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="活动类型" prop="activityType">
            <el-select 
              v-model="formData.activityType" 
              placeholder="请选择活动类型"
              style="width: 100%"
            >
              <el-option label="盲盒活动" value="blind_box" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="总通宝数量" prop="totalTongbao">
            <el-input-number
              v-model="formData.totalTongbao"
              :min="1000"
              :max="10000000"
              :step="1000"
              placeholder="请输入总通宝数量"
              style="width: 100%"
            />
            <div class="form-tip">
              建议设置为1000的整数倍，最少1000通宝
            </div>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="活动时长" prop="duration">
            <el-radio-group v-model="duration" @change="handleDurationChange">
              <el-radio :label="1">1天</el-radio>
              <el-radio :label="3">3天</el-radio>
              <el-radio :label="7">7天</el-radio>
              <el-radio :label="0">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              type="datetime"
              placeholder="请选择开始时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledStartDate"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              type="datetime"
              placeholder="请选择结束时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledEndDate"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="活动说明">
        <el-input
          v-model="activityDescription"
          type="textarea"
          :rows="4"
          placeholder="请输入活动说明（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { validators } from '@/utils'

interface BasicInfo {
  activityName: string
  activityType: 'blind_box'
  totalTongbao: number
  startTime: string
  endTime: string
}

const props = defineProps<{
  modelValue: BasicInfo
  errors?: Record<string, string>
}>()

const emit = defineEmits<{
  'update:modelValue': [value: BasicInfo]
  validate: [isValid: boolean]
}>()

const formRef = ref<FormInstance>()
const duration = ref(1) // 活动时长（天）
const activityDescription = ref('')

// 表单数据
const formData = reactive({ ...props.modelValue })

// 表单验证规则
const rules: FormRules = {
  activityName: [
    { validator: validators.required, trigger: 'blur' },
    { validator: validators.minLength(2), trigger: 'blur' },
    { validator: validators.maxLength(50), trigger: 'blur' }
  ],
  activityType: [
    { validator: validators.required, trigger: 'change' }
  ],
  totalTongbao: [
    { validator: validators.required, trigger: 'blur' },
    { validator: validators.min(1000), trigger: 'blur' },
    { validator: validators.max(10000000), trigger: 'blur' }
  ],
  startTime: [
    { validator: validators.required, trigger: 'change' }
  ],
  endTime: [
    { validator: validators.required, trigger: 'change' }
  ]
}

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 处理活动时长变化
const handleDurationChange = (days: number) => {
  if (days > 0 && formData.startTime) {
    const startDate = new Date(formData.startTime)
    const endDate = new Date(startDate.getTime() + days * 24 * 60 * 60 * 1000)
    formData.endTime = endDate.toISOString().slice(0, 19).replace('T', ' ')
  }
}

// 禁用开始日期
const disabledStartDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 禁用结束日期
const disabledEndDate = (time: Date) => {
  if (!formData.startTime) return true
  const startTime = new Date(formData.startTime).getTime()
  return time.getTime() < startTime + 24 * 60 * 60 * 1000
}

// 处理表单验证
const handleValidate = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('validate', true)
  } catch {
    emit('validate', false)
  }
}

// 监听开始时间变化，自动设置结束时间
watch(() => formData.startTime, (newStartTime) => {
  if (newStartTime && duration.value > 0) {
    handleDurationChange(duration.value)
  }
})

// 初始验证
nextTick(() => {
  handleValidate()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.basic-info-form {
  .form-tip {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    margin-top: $spacing-xs;
  }
  
  .el-form-item {
    margin-bottom: $spacing-lg;
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .basic-info-form {
    .el-col {
      margin-bottom: $spacing-md;
    }
  }
}
</style>
