<template>
  <div class="reward-card">
    <BaseCard 
      :class="cardClass"
      @click="handleClick"
      @touchstart="handleTouchStart"
      @touchend="handleTouchEnd"
    >
      <template #header>
        <div class="reward-header">
          <div class="reward-type">
            <StatusTag 
              :status="reward.rewardType === 'tongbao' ? 'success' : 'warning'"
              size="small"
            >
              {{ reward.rewardType === 'tongbao' ? '通宝' : '实物' }}
            </StatusTag>
          </div>
          
          <div class="reward-probability" v-if="showProbability">
            {{ reward.probability?.toFixed(2) }}%
          </div>
        </div>
      </template>
      
      <div class="reward-content">
        <!-- 奖品图标 -->
        <div class="reward-icon">
          <el-icon size="48" :class="iconClass">
            <component :is="rewardIcon" />
          </el-icon>
        </div>
        
        <!-- 奖品信息 -->
        <div class="reward-info">
          <h4 class="reward-name">{{ reward.rewardName }}</h4>
          
          <div class="reward-value" v-if="reward.rewardType === 'tongbao'">
            {{ formatTongbao(reward.tongbaoAmount) }}
          </div>
          
          <div class="reward-description" v-if="reward.physicalItem">
            {{ reward.physicalItem }}
          </div>
        </div>
        
        <!-- 稀有度指示器 -->
        <div class="rarity-indicator" v-if="rarityLevel">
          <div class="rarity-stars">
            <el-icon 
              v-for="i in 5" 
              :key="i"
              :class="{ 'is-active': i <= rarityLevel }"
              class="star-icon"
            >
              <Star />
            </el-icon>
          </div>
          <div class="rarity-text">{{ getRarityText() }}</div>
        </div>
      </div>
      
      <template #footer>
        <div class="reward-footer">
          <div class="quantity-info">
            <span class="label">每日：</span>
            <span class="value">{{ reward.dailyQuantity }}</span>
          </div>
          
          <div class="remaining-info" v-if="reward.remainingQuantity !== undefined">
            <span class="label">剩余：</span>
            <span 
              class="value"
              :class="{ 'low-stock': isLowStock }"
            >
              {{ reward.remainingQuantity }}
            </span>
          </div>
        </div>
      </template>
    </BaseCard>
    
    <!-- 长按提示 -->
    <div class="long-press-hint" v-if="showLongPressHint">
      长按查看详情
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Star, Coin, Gift, Trophy } from '@element-plus/icons-vue'
import { BaseCard, StatusTag } from '@/components/common'
import { formatTongbao } from '@/utils'
import type { Reward } from '@/types'

interface Props {
  reward: Reward
  showProbability?: boolean
  showLongPressHint?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showProbability: true,
  showLongPressHint: true,
  clickable: true
})

const emit = defineEmits<{
  click: [reward: Reward]
  longPress: [reward: Reward]
}>()

const longPressTimer = ref<NodeJS.Timeout | null>(null)
const isLongPressing = ref(false)

// 计算属性
const cardClass = computed(() => [
  'reward-item-card',
  {
    'is-clickable': props.clickable,
    'is-rare': isRare.value,
    'is-epic': isEpic.value,
    'is-legendary': isLegendary.value,
    'is-low-stock': isLowStock.value,
    'is-long-pressing': isLongPressing.value
  }
])

const rewardIcon = computed(() => {
  if (props.reward.rewardType === 'tongbao') {
    return Coin
  } else if (isLegendary.value) {
    return Trophy
  } else {
    return Gift
  }
})

const iconClass = computed(() => [
  'reward-icon-component',
  {
    'icon-tongbao': props.reward.rewardType === 'tongbao',
    'icon-physical': props.reward.rewardType === 'physical',
    'icon-rare': isRare.value,
    'icon-epic': isEpic.value,
    'icon-legendary': isLegendary.value
  }
])

// 稀有度计算（基于概率）
const rarityLevel = computed(() => {
  if (!props.reward.probability) return 0
  
  const prob = props.reward.probability
  if (prob <= 1) return 5 // 传说
  if (prob <= 5) return 4 // 史诗
  if (prob <= 15) return 3 // 稀有
  if (prob <= 30) return 2 // 不常见
  return 1 // 普通
})

const isRare = computed(() => rarityLevel.value >= 3)
const isEpic = computed(() => rarityLevel.value >= 4)
const isLegendary = computed(() => rarityLevel.value >= 5)

const isLowStock = computed(() => {
  return props.reward.remainingQuantity !== undefined && 
         props.reward.remainingQuantity < 5
})

// 方法
const getRarityText = () => {
  const rarityMap: Record<number, string> = {
    5: '传说',
    4: '史诗',
    3: '稀有',
    2: '不常见',
    1: '普通'
  }
  return rarityMap[rarityLevel.value] || ''
}

const handleClick = () => {
  if (!props.clickable) return
  emit('click', props.reward)
}

const handleTouchStart = () => {
  if (!props.clickable) return
  
  isLongPressing.value = true
  longPressTimer.value = setTimeout(() => {
    emit('longPress', props.reward)
    isLongPressing.value = false
  }, 800) // 800ms长按
}

const handleTouchEnd = () => {
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value)
    longPressTimer.value = null
  }
  isLongPressing.value = false
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.reward-card {
  position: relative;
  
  .reward-item-card {
    transition: all 0.3s ease;
    cursor: default;
    
    &.is-clickable {
      cursor: pointer;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
    }
    
    &.is-rare {
      border: 2px solid $warning-color;
      background: linear-gradient(135deg, lighten($warning-color, 45%) 0%, $background-color-white 100%);
    }
    
    &.is-epic {
      border: 2px solid #9C27B0;
      background: linear-gradient(135deg, lighten(#9C27B0, 45%) 0%, $background-color-white 100%);
      box-shadow: 0 0 20px rgba(#9C27B0, 0.3);
    }
    
    &.is-legendary {
      border: 2px solid #FF5722;
      background: linear-gradient(135deg, lighten(#FF5722, 45%) 0%, $background-color-white 100%);
      box-shadow: 0 0 30px rgba(#FF5722, 0.4);
      animation: legendary-glow 2s ease-in-out infinite alternate;
    }
    
    &.is-low-stock {
      .reward-footer {
        .remaining-info {
          .value.low-stock {
            color: $error-color;
            font-weight: 700;
            animation: pulse 1s ease-in-out infinite;
          }
        }
      }
    }
    
    &.is-long-pressing {
      transform: scale(0.95);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    .reward-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .reward-probability {
        font-size: $font-size-sm;
        font-weight: 600;
        color: $primary-color;
      }
    }
    
    .reward-content {
      text-align: center;
      
      .reward-icon {
        margin-bottom: $spacing-md;
        
        .reward-icon-component {
          transition: all 0.3s ease;
          
          &.icon-tongbao {
            color: $success-color;
          }
          
          &.icon-physical {
            color: $warning-color;
          }
          
          &.icon-rare {
            color: $warning-color;
            filter: drop-shadow(0 0 8px rgba($warning-color, 0.5));
          }
          
          &.icon-epic {
            color: #9C27B0;
            filter: drop-shadow(0 0 12px rgba(#9C27B0, 0.6));
          }
          
          &.icon-legendary {
            color: #FF5722;
            filter: drop-shadow(0 0 16px rgba(#FF5722, 0.7));
            animation: icon-pulse 1.5s ease-in-out infinite;
          }
        }
      }
      
      .reward-info {
        margin-bottom: $spacing-md;
        
        .reward-name {
          margin: 0 0 $spacing-sm 0;
          font-size: $font-size-base;
          color: $text-color-primary;
        }
        
        .reward-value {
          font-size: $font-size-lg;
          font-weight: 600;
          color: $success-color;
          margin-bottom: $spacing-xs;
        }
        
        .reward-description {
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }
      
      .rarity-indicator {
        .rarity-stars {
          display: flex;
          justify-content: center;
          gap: 2px;
          margin-bottom: $spacing-xs;
          
          .star-icon {
            font-size: 14px;
            color: $border-color-light;
            transition: color 0.3s ease;
            
            &.is-active {
              color: #FFD700;
              filter: drop-shadow(0 0 4px rgba(#FFD700, 0.6));
            }
          }
        }
        
        .rarity-text {
          font-size: $font-size-sm;
          font-weight: 600;
          color: $text-color-secondary;
        }
      }
    }
    
    .reward-footer {
      display: flex;
      justify-content: space-between;
      font-size: $font-size-sm;
      
      .quantity-info,
      .remaining-info {
        .label {
          color: $text-color-secondary;
        }
        
        .value {
          color: $text-color-primary;
          font-weight: 500;
        }
      }
    }
  }
  
  .long-press-hint {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: $font-size-sm;
    color: $text-color-secondary;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 0;
    animation: hint-fade-in 2s ease-in-out infinite;
  }
}

@keyframes legendary-glow {
  from {
    box-shadow: 0 0 20px rgba(#FF5722, 0.3);
  }
  to {
    box-shadow: 0 0 40px rgba(#FF5722, 0.6);
  }
}

@keyframes icon-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes hint-fade-in {
  0%, 70% {
    opacity: 0;
  }
  85%, 100% {
    opacity: 1;
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .reward-card {
    .reward-item-card {
      &:hover {
        transform: none;
      }
      
      &.is-clickable:active {
        transform: scale(0.95);
      }
    }
  }
}
</style>
