import type { App, DirectiveBinding } from 'vue'
import { usePermission } from '@/composables/usePermission'
import type { UserRole } from '@/types'

// 权限指令值类型
interface PermissionValue {
  permission?: string
  role?: UserRole | UserRole[]
  any?: boolean // 是否满足任一条件即可
}

// 权限指令
export const permissionDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<PermissionValue | string>) {
    checkPermission(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding<PermissionValue | string>) {
    checkPermission(el, binding)
  }
}

function checkPermission(el: HTMLElement, binding: DirectiveBinding<PermissionValue | string>) {
  const { hasPermission, hasRole, hasAnyRole } = usePermission()
  const value = binding.value
  
  let hasAccess = false
  
  if (typeof value === 'string') {
    // 简单权限检查
    hasAccess = hasPermission(value as any)
  } else if (typeof value === 'object') {
    const { permission, role, any = false } = value
    
    const checks: boolean[] = []
    
    // 检查权限
    if (permission) {
      checks.push(hasPermission(permission as any))
    }
    
    // 检查角色
    if (role) {
      if (Array.isArray(role)) {
        checks.push(hasAnyRole(role))
      } else {
        checks.push(hasRole(role))
      }
    }
    
    // 根据any参数决定是否满足任一条件或所有条件
    if (any) {
      hasAccess = checks.some(check => check)
    } else {
      hasAccess = checks.every(check => check)
    }
  }
  
  // 根据权限检查结果显示或隐藏元素
  if (!hasAccess) {
    // 移除元素或隐藏
    if (binding.modifiers.hide) {
      el.style.display = 'none'
    } else if (binding.modifiers.disable) {
      el.setAttribute('disabled', 'true')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
    } else {
      el.remove()
    }
  } else {
    // 恢复元素显示
    if (binding.modifiers.hide) {
      el.style.display = ''
    } else if (binding.modifiers.disable) {
      el.removeAttribute('disabled')
      el.style.opacity = ''
      el.style.cursor = ''
    }
  }
}

// 角色指令（简化版）
export const roleDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<UserRole | UserRole[]>) {
    checkRole(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding<UserRole | UserRole[]>) {
    checkRole(el, binding)
  }
}

function checkRole(el: HTMLElement, binding: DirectiveBinding<UserRole | UserRole[]>) {
  const { hasRole, hasAnyRole } = usePermission()
  const roles = binding.value
  
  let hasAccess = false
  
  if (Array.isArray(roles)) {
    hasAccess = hasAnyRole(roles)
  } else {
    hasAccess = hasRole(roles)
  }
  
  if (!hasAccess) {
    if (binding.modifiers.hide) {
      el.style.display = 'none'
    } else if (binding.modifiers.disable) {
      el.setAttribute('disabled', 'true')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
    } else {
      el.remove()
    }
  } else {
    if (binding.modifiers.hide) {
      el.style.display = ''
    } else if (binding.modifiers.disable) {
      el.removeAttribute('disabled')
      el.style.opacity = ''
      el.style.cursor = ''
    }
  }
}

// 注册指令
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permissionDirective)
  app.directive('role', roleDirective)
}

// 使用示例：
/*
<!-- 基于权限显示/隐藏 -->
<el-button v-permission="'canCreateActivity'">创建活动</el-button>

<!-- 基于角色显示/隐藏 -->
<el-button v-role="'alliance_leader'">管理员功能</el-button>
<el-button v-role="['alliance_leader', 'partner']">管理功能</el-button>

<!-- 使用修饰符 -->
<el-button v-permission.hide="'canCreateActivity'">创建活动</el-button>
<el-button v-permission.disable="'canCreateActivity'">创建活动</el-button>

<!-- 复杂权限检查 -->
<el-button v-permission="{ permission: 'canCreateActivity', role: 'alliance_leader' }">
  创建活动
</el-button>

<!-- 满足任一条件 -->
<el-button v-permission="{ role: ['alliance_leader', 'partner'], any: true }">
  管理功能
</el-button>
*/
