<template>
  <div class="empty-state">
    <div class="empty-content">
      <el-icon class="empty-icon" :size="iconSize">
        <component :is="iconComponent" />
      </el-icon>
      
      <h3 class="empty-title">{{ title }}</h3>
      
      <p class="empty-description" v-if="description">
        {{ description }}
      </p>
      
      <div class="empty-actions" v-if="$slots.actions">
        <slot name="actions" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Box, 
  DocumentRemove, 
  Search, 
  Warning,
  InfoFilled,
  CircleClose
} from '@element-plus/icons-vue'

interface Props {
  type?: 'default' | 'no-data' | 'no-result' | 'error' | 'network' | 'permission'
  title?: string
  description?: string
  size?: 'small' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'default'
})

const iconSize = computed(() => {
  const sizeMap = {
    small: 48,
    default: 64,
    large: 80
  }
  return sizeMap[props.size]
})

const iconComponent = computed(() => {
  const iconMap = {
    default: Box,
    'no-data': DocumentRemove,
    'no-result': Search,
    error: CircleClose,
    network: Warning,
    permission: InfoFilled
  }
  return iconMap[props.type]
})

const title = computed(() => {
  if (props.title) return props.title
  
  const titleMap = {
    default: '暂无数据',
    'no-data': '暂无数据',
    'no-result': '无搜索结果',
    error: '出错了',
    network: '网络异常',
    permission: '无权限访问'
  }
  return titleMap[props.type]
})

const description = computed(() => {
  if (props.description) return props.description
  
  const descMap = {
    default: '',
    'no-data': '当前还没有任何数据',
    'no-result': '请尝试调整搜索条件',
    error: '页面出现了一些问题',
    network: '请检查网络连接后重试',
    permission: '您没有权限访问此内容'
  }
  return descMap[props.type]
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: $spacing-xl;
  
  .empty-content {
    text-align: center;
    max-width: 400px;
    
    .empty-icon {
      color: $text-color-disabled;
      margin-bottom: $spacing-lg;
    }
    
    .empty-title {
      margin: 0 0 $spacing-md 0;
      font-size: $font-size-lg;
      font-weight: 500;
      color: $text-color-secondary;
    }
    
    .empty-description {
      margin: 0 0 $spacing-lg 0;
      font-size: $font-size-sm;
      color: $text-color-disabled;
      line-height: $line-height-lg;
    }
    
    .empty-actions {
      display: flex;
      gap: $spacing-md;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}
</style>
