<template>
  <div class="task-card">
    <BaseCard 
      :class="[
        'task-item-card',
        {
          'is-completed': task.completed,
          'is-available': canComplete
        }
      ]"
    >
      <template #header>
        <div class="task-header">
          <div class="task-info">
            <h4>{{ task.taskName }}</h4>
            <StatusTag :status="getTaskStatus()" size="small" />
          </div>
          <div class="task-reward">
            <el-icon><Trophy /></el-icon>
            <span>{{ task.rewardChances }}次</span>
          </div>
        </div>
      </template>
      
      <div class="task-content">
        <div class="task-description">
          <div class="desc-item">
            <span class="label">任务类型：</span>
            <span class="value">{{ getTaskTypeText(task.taskType) }}</span>
          </div>
          <div class="desc-item">
            <span class="label">目标值：</span>
            <span class="value">{{ task.targetValue }}</span>
          </div>
          <div class="desc-item">
            <span class="label">刷新类型：</span>
            <span class="value">{{ getRefreshTypeText(task.refreshType) }}</span>
          </div>
        </div>
        
        <!-- 任务进度 -->
        <div class="task-progress">
          <div class="progress-info">
            <span>进度：{{ task.currentProgress || 0 }} / {{ task.targetValue }}</span>
            <span>{{ progressPercentage }}%</span>
          </div>
          <el-progress 
            :percentage="progressPercentage" 
            :color="getProgressColor()"
            :stroke-width="6"
          />
        </div>
        
        <!-- 刷新倒计时 -->
        <div v-if="showRefreshCountdown" class="refresh-countdown">
          <el-icon><Clock /></el-icon>
          <span>{{ refreshCountdownText }}</span>
        </div>
      </div>
      
      <template #footer>
        <div class="task-actions">
          <el-button 
            v-if="task.completed"
            type="success" 
            disabled
            size="small"
          >
            <el-icon><CircleCheck /></el-icon>
            已完成
          </el-button>
          
          <el-button 
            v-else-if="canComplete"
            type="primary" 
            @click="handleComplete"
            :loading="completing"
            size="small"
          >
            <el-icon><Trophy /></el-icon>
            领取奖励
          </el-button>
          
          <el-button 
            v-else
            disabled
            size="small"
          >
            <el-icon><Clock /></el-icon>
            进行中
          </el-button>
        </div>
      </template>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Trophy, Clock, CircleCheck } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { BaseCard, StatusTag } from '@/components/common'
import { getTaskTypeText, getRefreshTypeText, formatCountdown } from '@/utils'
import type { Task } from '@/types'

interface Props {
  task: Task
}

const props = defineProps<Props>()

const emit = defineEmits<{
  complete: [taskId: number]
}>()

const completing = ref(false)

// 计算属性
const progressPercentage = computed(() => {
  const current = props.task.currentProgress || 0
  const target = props.task.targetValue
  return Math.min(100, Math.round((current / target) * 100))
})

const canComplete = computed(() => {
  return !props.task.completed && 
         (props.task.currentProgress || 0) >= props.task.targetValue
})

const showRefreshCountdown = computed(() => {
  return props.task.refreshType !== 'never' && 
         props.task.refreshCountdown && 
         props.task.refreshCountdown > 0
})

const refreshCountdownText = computed(() => {
  if (!props.task.refreshCountdown) return ''
  return `${formatCountdown(props.task.refreshCountdown)}后刷新`
})

// 方法
const getTaskStatus = () => {
  if (props.task.completed) return 'completed'
  if (canComplete.value) return 'success'
  return 'in_progress'
}

const getProgressColor = () => {
  if (props.task.completed) return '#67C23A'
  if (canComplete.value) return '#67C23A'
  return '#409EFF'
}

const handleComplete = async () => {
  if (!canComplete.value) return
  
  completing.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    emit('complete', props.task.id!)
  } catch (error) {
    console.error('Failed to complete task:', error)
    ElMessage.error('领取奖励失败，请重试')
  } finally {
    completing.value = false
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.task-card {
  .task-item-card {
    transition: all 0.3s ease;
    
    &.is-completed {
      background-color: lighten($success-color, 45%);
      border-color: lighten($success-color, 25%);
    }
    
    &.is-available {
      border-color: $primary-color;
      box-shadow: 0 2px 8px rgba($primary-color, 0.2);
    }
    
    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .task-info {
        flex: 1;
        
        h4 {
          margin: 0 0 $spacing-xs 0;
          color: $text-color-primary;
          font-size: $font-size-base;
        }
      }
      
      .task-reward {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        color: $warning-color;
        font-weight: 600;
        font-size: $font-size-sm;
      }
    }
    
    .task-content {
      .task-description {
        margin-bottom: $spacing-md;
        
        .desc-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: $spacing-xs;
          
          .label {
            color: $text-color-secondary;
            font-size: $font-size-sm;
          }
          
          .value {
            color: $text-color-primary;
            font-size: $font-size-sm;
            font-weight: 500;
          }
        }
      }
      
      .task-progress {
        margin-bottom: $spacing-md;
        
        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: $spacing-xs;
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }
      
      .refresh-countdown {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        color: $text-color-secondary;
        font-size: $font-size-sm;
      }
    }
    
    .task-actions {
      display: flex;
      justify-content: center;
    }
  }
}
</style>
