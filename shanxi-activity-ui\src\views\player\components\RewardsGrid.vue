<template>
  <div class="rewards-grid">
    <div class="grid-header" v-if="showHeader">
      <h3>{{ title }}</h3>
      <div class="grid-controls">
        <el-radio-group v-model="sortBy" size="small" @change="handleSort">
          <el-radio-button label="probability">概率</el-radio-button>
          <el-radio-button label="value">价值</el-radio-button>
          <el-radio-button label="rarity">稀有度</el-radio-button>
        </el-radio-group>
        
        <el-button 
          size="small" 
          @click="showAllRewards = !showAllRewards"
          :type="showAllRewards ? 'primary' : 'default'"
        >
          {{ showAllRewards ? '收起' : '查看全部' }}
        </el-button>
      </div>
    </div>
    
    <div class="grid-container">
      <LoadingSpinner v-if="loading" text="加载奖品..." />
      
      <div v-else-if="displayRewards.length === 0" class="empty-container">
        <EmptyState 
          type="no-data" 
          title="暂无奖品"
          description="当前活动没有设置奖品"
        />
      </div>
      
      <div v-else class="rewards-list">
        <TransitionGroup name="reward-item" tag="div" class="rewards-wrapper">
          <div 
            v-for="reward in displayRewards" 
            :key="reward.id"
            class="reward-item"
          >
            <RewardCard 
              :reward="reward"
              :show-probability="showProbability"
              :show-long-press-hint="showLongPressHint"
              @click="handleRewardClick"
              @long-press="handleRewardLongPress"
            />
          </div>
        </TransitionGroup>
        
        <!-- 展开/收起按钮 -->
        <div class="expand-button" v-if="hasMoreRewards && !showHeader">
          <el-button 
            @click="showAllRewards = !showAllRewards"
            :type="showAllRewards ? 'info' : 'primary'"
            size="large"
            round
          >
            {{ showAllRewards ? '收起奖品' : `查看全部奖品 (${rewards.length})` }}
            <el-icon class="expand-icon" :class="{ 'is-expanded': showAllRewards }">
              <ArrowDown />
            </el-icon>
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 奖品详情弹窗 -->
    <RewardDetailDialog 
      v-model="showDetailDialog"
      :reward="selectedReward"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { LoadingSpinner, EmptyState } from '@/components/common'
import RewardCard from './RewardCard.vue'
import RewardDetailDialog from './RewardDetailDialog.vue'
import type { Reward } from '@/types'

interface Props {
  rewards: Reward[]
  loading?: boolean
  title?: string
  showHeader?: boolean
  showProbability?: boolean
  showLongPressHint?: boolean
  initialLimit?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  title: '奖品列表',
  showHeader: true,
  showProbability: true,
  showLongPressHint: true,
  initialLimit: 6
})

const emit = defineEmits<{
  rewardClick: [reward: Reward]
  rewardLongPress: [reward: Reward]
}>()

// 响应式数据
const sortBy = ref<'probability' | 'value' | 'rarity'>('probability')
const showAllRewards = ref(false)
const showDetailDialog = ref(false)
const selectedReward = ref<Reward | null>(null)

// 计算属性
const sortedRewards = computed(() => {
  const rewardsCopy = [...props.rewards]
  
  switch (sortBy.value) {
    case 'probability':
      return rewardsCopy.sort((a, b) => (a.probability || 0) - (b.probability || 0))
    
    case 'value':
      return rewardsCopy.sort((a, b) => {
        const aValue = a.rewardType === 'tongbao' ? a.tongbaoAmount : 0
        const bValue = b.rewardType === 'tongbao' ? b.tongbaoAmount : 0
        return bValue - aValue
      })
    
    case 'rarity':
      return rewardsCopy.sort((a, b) => {
        const getRarity = (reward: Reward) => {
          if (!reward.probability) return 0
          if (reward.probability <= 1) return 5
          if (reward.probability <= 5) return 4
          if (reward.probability <= 15) return 3
          if (reward.probability <= 30) return 2
          return 1
        }
        return getRarity(b) - getRarity(a)
      })
    
    default:
      return rewardsCopy
  }
})

const displayRewards = computed(() => {
  if (showAllRewards.value) {
    return sortedRewards.value
  }
  return sortedRewards.value.slice(0, props.initialLimit)
})

const hasMoreRewards = computed(() => {
  return props.rewards.length > props.initialLimit
})

// 方法
const handleSort = () => {
  // 排序变化时的处理逻辑
}

const handleRewardClick = (reward: Reward) => {
  emit('rewardClick', reward)
}

const handleRewardLongPress = (reward: Reward) => {
  selectedReward.value = reward
  showDetailDialog.value = true
  emit('rewardLongPress', reward)
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.rewards-grid {
  .grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-md;
    border-bottom: 1px solid $border-color-light;
    
    h3 {
      margin: 0;
      color: $text-color-primary;
    }
    
    .grid-controls {
      display: flex;
      align-items: center;
      gap: $spacing-md;
    }
  }
  
  .grid-container {
    .empty-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
    }
    
    .rewards-list {
      .rewards-wrapper {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: $spacing-lg;
        margin-bottom: $spacing-xl;
        
        .reward-item {
          transition: all 0.3s ease;
        }
      }
      
      .expand-button {
        text-align: center;
        
        .expand-icon {
          margin-left: $spacing-xs;
          transition: transform 0.3s ease;
          
          &.is-expanded {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}

// 过渡动画
.reward-item-enter-active,
.reward-item-leave-active {
  transition: all 0.5s ease;
}

.reward-item-enter-from {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.reward-item-leave-to {
  opacity: 0;
  transform: translateY(-30px) scale(0.9);
}

.reward-item-move {
  transition: transform 0.5s ease;
}

// 移动端适配
@media (max-width: $screen-lg) {
  .rewards-grid {
    .rewards-wrapper {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
    }
  }
}

@media (max-width: $screen-md) {
  .rewards-grid {
    .grid-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;
      
      .grid-controls {
        width: 100%;
        justify-content: space-between;
      }
    }
    
    .rewards-wrapper {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
      gap: $spacing-md !important;
    }
  }
}

@media (max-width: $screen-sm) {
  .rewards-grid {
    .rewards-wrapper {
      grid-template-columns: 1fr !important;
    }
    
    .grid-controls {
      flex-direction: column;
      gap: $spacing-sm;
      
      .el-radio-group {
        width: 100%;
      }
    }
  }
}
</style>
