目录
陕西平台活动开放工具	1
一、 结构图	1
二、 需求目的	2
三、 功能简述	2
四、 详细需求	2
1. 活动发起人	2
2. 活动参与人	2
3. 活动配置以及活动奖励注入	2
① 活动入口	2
② 活动配置	3
③ 活动展示	6
4. 活动数据记录	9
① 记录位置	9
② 历史活动	9
③ 活动数据	10


陕西平台活动开放工具


   一、 结构图

   二、 需求目的
为代理提供活动工具，让代理能使用工具快速发起活动，增加用户的活跃和留存 

   三、 功能简述
盟主和各级合伙人可使用该工具为其下属的所有玩家（包括直接和间接下属）发起一个活动，由发起人提供通宝奖励，下属玩家通过活动领取通宝奖励

   四、 详细需求
1. 活动发起人
盟主、以及合伙人
2. 活动参与人
发起人的下级（包括直接和间接下级）
3. 活动配置以及活动奖励注入
　　①　 活动入口

在盟主和一级合伙人的功能栏新增一个活动图标，点击进入后即可配置活动
　　②　 活动配置
活动配置分为两步：活动基础配置、活动详细配置
活动基础配置：

点击“活动”按钮后进入配置页面
* 活动名
设置的活动名称，玩家会在活动图标看见活动名称
* 活动类型
选择要创建的活动的类型，目前这期只有一个类型——盲盒活动
* 活动通宝
本次活动中，需要放入的奖励通宝，该值由发起人填写，活动成功创建后扣除发起人身上对应数量的通宝
备注：当发起人身上通报不足填写的通宝时，点击下一步时弹窗提醒：“身上的通宝数不足，请补充后再尝试”
* 活动时间
选择活动开始和活动结束的时间，时间精确到分钟即可
当以上三个配置项配置完毕后，下方的下一步按钮由置灰状态变为可点击状态，点击后进入下一个配置
活动详细配置：
活动详细配置分为两部分：奖励配置、任务配置
可上下滑动
奖励配置：
配置一条奖品的详细数据
* 奖励名
该条奖品在活动中显示的名字，例如：一等奖 500通宝、一等奖 300块京东卡
* 权重
该条奖品对应所有奖品的权重配比，填写项后的概率显示会根据填写后的结果自动计算最终的概率
* 奖励通宝
该条奖品奖励的通宝数量，若该条奖品不为通宝奖励，则该项写0即可
* 份数
该条奖励的份数，当玩家抽走奖励后，份数会相应的减少，当份数为0后玩家不可再抽中次奖品。注：份数为每日份数，每天0点自动更新
* 新增奖品配置
点击新的配置项后的“+”可增加一条新的奖品配置项
配置任务：
* 任务类型
可选以下的任务类型进行配置：
1) 登录任务
登录即可完成的任务
2) 局数任务
完成任意对应的局数（大局数）即可完成的任务	
3) 贡献任务（服务费任务）
达到相应的服务费即可完成的任务
* 任务数值
针对当前配置的任务配置对应数值，数值不可配置为0，登录任务该项固定为1
例如登录任务，配置数值为1的话，玩家登录游戏即可完成任务，若配置为2则玩家需要登录两次才能完成该任务（所以正常配置登录任务仅可配置为1）
* 奖励次数
完成该项任务或可获得的抽奖次数
* 任务刷新时间
为一个下拉可选项，可选每日，每周，不刷新
每日刷新则为每天0点刷新任务进度，并重置任务。同理为每周刷新（每周一0点）和不刷新

　　③　 活动展示
活动入口：

当发起人发动活动后，其下属玩家功能栏会新增活动的图标（若有该玩家有多个活动可参与，则显示多个图标，每个活动单独处理），图标上显示未发起人设置的活动名

* 奖池	
奖池中显示当前活动剩余的奖励，为发起人创建活动时注入的通宝
* 活动时间
显示当前活动的持续时间，当时间结束后，入口图标消失
* 次数任务
显示发起人配置的所有任务，做成可上下滑动，排序规则如下:
1) 优先按照类型排序：登录任务，局数任务，服务费任务
2) 其次同类型的任务按照数值由小到大排序
3) 当任务完成后，自动发放次数奖励，完成的任务放到最后面
任务刷新倒计时按照发起任务时配置的任务刷新时间进行倒计时，按照天/小时/分的格式显示，0天不显示天
* 盲盒开启
点击“打开盲盒”后，按照配置的奖品进行抽奖
* 帮助
点击“？”后，弹出帮助页，写活动有关规则

* 记录

记录玩家在本期活动中的所有中奖记录

左上角可选择查看全部玩家或我的记录
选择全部玩家此处则展示所有的玩家中奖记录，选择我的则筛选显示我的中奖记录
此处的记录最多展示50条
注：我的记录也是最多展示50条而不是从全部的50条里面筛选
* 奖品展示

按住图标不放展示奖品，奖品显示为发起活动时的商品配置，松手自动关闭。
* 跑马灯功能
按照玩家的中奖时间，播放最近的一条中奖消息，播放完毕后，若30秒内无新的玩家中奖，则在最近的10条记录内循环播放。
若持续有玩家中奖，则在一条消息播放完毕后连续播放下一条消息。
4. 活动数据记录
　　①　 记录位置
盟主和各级合伙人创建活动页有“历史活动”，点击进去能看到以往创建的活动（包括当前正在进行中的活动）
权限说明：
盟主：能看到所有活动，历史活动页每行数据新增一条“发起人昵称”
各级合伙人：只能看到自己创建的活动
　　②　 历史活动

* 活动类型：为创建活动时选择的活动类型，目前仅有盲盒活动一种
* 活动通宝：创建活动时注入的通宝
* 通宝结余：截至活动结束时，奖池中剩余的通宝数量
* 活动时间：创建活动时设置的活动时间
* 活动状态：当前活动的状态，分为3种：未开始，进行中，已结束

发起人可在活动中途操作关闭活动
　　③　 活动数据
点击任意一条历史活动行后的“数据”按钮，打开数据页

* 参与人数：活动当前的参与人数，若是正在进行中的活动，显示实时参与过的人数
* 已发奖励：当前已发放给玩家的通宝奖励，其他奖励不算在此数据中
* 玩家统计：以下统计数据均按照单个玩家汇总数据进行统计，统计数据包含：玩家昵称，玩家id，总开启次数，累计中奖通宝
* 实物奖励：统计玩家中奖中所有实物类型的奖品，按照奖品名*数量的格式显示，外面按照适应性显示缩略信息，点击后小弹窗展开显示详细信息


