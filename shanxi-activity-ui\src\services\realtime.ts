// 实时数据更新服务
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useActivityStore, useDrawStore, useUserStore } from '@/stores'
import type { DrawRecord, MarqueeMessage } from '@/types'

export interface RealtimeConfig {
  enableWebSocket?: boolean
  enablePolling?: boolean
  pollingInterval?: number
  reconnectAttempts?: number
  reconnectDelay?: number
}

export class RealtimeService {
  private ws: WebSocket | null = null
  private pollingTimer: NodeJS.Timeout | null = null
  private reconnectTimer: NodeJS.Timeout | null = null
  private reconnectAttempts = 0
  private isConnected = ref(false)
  private config: Required<RealtimeConfig>
  
  // 事件监听器
  private listeners = reactive<{
    [key: string]: Array<(data: any) => void>
  }>({})

  constructor(config: RealtimeConfig = {}) {
    this.config = {
      enableWebSocket: true,
      enablePolling: true,
      pollingInterval: 30000, // 30秒
      reconnectAttempts: 5,
      reconnectDelay: 3000,
      ...config
    }
  }

  // 初始化连接
  async initialize() {
    if (this.config.enableWebSocket) {
      await this.connectWebSocket()
    }
    
    if (this.config.enablePolling) {
      this.startPolling()
    }
  }

  // WebSocket连接
  private async connectWebSocket() {
    try {
      const wsUrl = this.getWebSocketUrl()
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.isConnected.value = true
        this.reconnectAttempts = 0
        this.emit('connected', {})
      }
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleMessage(data)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }
      
      this.ws.onclose = () => {
        console.log('WebSocket disconnected')
        this.isConnected.value = false
        this.emit('disconnected', {})
        this.handleReconnect()
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        this.emit('error', { error })
      }
      
    } catch (error) {
      console.error('Failed to connect WebSocket:', error)
      this.handleReconnect()
    }
  }

  // 处理WebSocket消息
  private handleMessage(data: any) {
    const { type, payload } = data
    
    switch (type) {
      case 'new_draw_record':
        this.handleNewDrawRecord(payload)
        break
        
      case 'activity_update':
        this.handleActivityUpdate(payload)
        break
        
      case 'task_progress_update':
        this.handleTaskProgressUpdate(payload)
        break
        
      case 'marquee_message':
        this.handleMarqueeMessage(payload)
        break
        
      case 'user_balance_update':
        this.handleUserBalanceUpdate(payload)
        break
        
      default:
        console.log('Unknown message type:', type)
    }
    
    // 触发通用事件
    this.emit(type, payload)
  }

  // 处理新的抽奖记录
  private handleNewDrawRecord(record: DrawRecord) {
    const drawStore = useDrawStore()
    drawStore.addNewRecord(record)
    
    // 显示中奖通知
    if (record.isMyRecord) {
      ElMessage.success(`恭喜您获得 ${record.rewardName}！`)
    }
  }

  // 处理活动更新
  private handleActivityUpdate(activityData: any) {
    const activityStore = useActivityStore()
    activityStore.updateActivityInList(activityData)
  }

  // 处理任务进度更新
  private handleTaskProgressUpdate(taskData: any) {
    // 更新任务进度
    console.log('Task progress updated:', taskData)
  }

  // 处理跑马灯消息
  private handleMarqueeMessage(message: MarqueeMessage) {
    const drawStore = useDrawStore()
    drawStore.addNewMarqueeMessage(message)
  }

  // 处理用户余额更新
  private handleUserBalanceUpdate(balanceData: any) {
    const userStore = useUserStore()
    userStore.updateTongbaoBalance(balanceData.balance)
  }

  // 轮询机制
  private startPolling() {
    this.pollingTimer = setInterval(() => {
      this.pollData()
    }, this.config.pollingInterval)
  }

  private async pollData() {
    try {
      // 轮询最新数据
      await this.pollActivityData()
      await this.pollDrawRecords()
      await this.pollUserBalance()
    } catch (error) {
      console.error('Polling failed:', error)
    }
  }

  private async pollActivityData() {
    // 轮询活动数据
    // 这里应该调用API获取最新的活动数据
  }

  private async pollDrawRecords() {
    // 轮询抽奖记录
    // 这里应该调用API获取最新的抽奖记录
  }

  private async pollUserBalance() {
    // 轮询用户余额
    // 这里应该调用API获取最新的用户余额
  }

  // 重连机制
  private handleReconnect() {
    if (this.reconnectAttempts >= this.config.reconnectAttempts) {
      console.log('Max reconnect attempts reached')
      return
    }
    
    this.reconnectAttempts++
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`)
    
    this.reconnectTimer = setTimeout(() => {
      this.connectWebSocket()
    }, this.config.reconnectDelay)
  }

  // 获取WebSocket URL
  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/ws/activity`
  }

  // 事件监听
  on(event: string, callback: (data: any) => void) {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event].push(callback)
  }

  // 移除事件监听
  off(event: string, callback?: (data: any) => void) {
    if (!this.listeners[event]) return
    
    if (callback) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    } else {
      this.listeners[event] = []
    }
  }

  // 触发事件
  private emit(event: string, data: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Event callback error:', error)
        }
      })
    }
  }

  // 发送消息
  send(type: string, payload: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type, payload }))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  // 销毁连接
  destroy() {
    // 清理WebSocket
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    // 清理轮询
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
    }
    
    // 清理重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    // 清理事件监听器
    Object.keys(this.listeners).forEach(event => {
      this.listeners[event] = []
    })
    
    this.isConnected.value = false
  }

  // 获取连接状态
  get connected() {
    return this.isConnected.value
  }
}

// 创建全局实例
export const realtimeService = new RealtimeService()

// Vue组合式函数
export function useRealtime() {
  return {
    service: realtimeService,
    connected: realtimeService.connected,
    
    // 便捷方法
    onNewDrawRecord: (callback: (record: DrawRecord) => void) => {
      realtimeService.on('new_draw_record', callback)
    },
    
    onActivityUpdate: (callback: (activity: any) => void) => {
      realtimeService.on('activity_update', callback)
    },
    
    onMarqueeMessage: (callback: (message: MarqueeMessage) => void) => {
      realtimeService.on('marquee_message', callback)
    }
  }
}
