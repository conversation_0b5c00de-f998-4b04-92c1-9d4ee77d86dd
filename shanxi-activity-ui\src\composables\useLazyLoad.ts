import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// 懒加载配置
interface LazyLoadOptions {
  root?: Element | null
  rootMargin?: string
  threshold?: number | number[]
  once?: boolean
}

// 图片懒加载
export function useImageLazyLoad(options: LazyLoadOptions = {}) {
  const {
    root = null,
    rootMargin = '50px',
    threshold = 0.1,
    once = true
  } = options

  const imageRefs = ref<HTMLImageElement[]>([])
  const observer = ref<IntersectionObserver | null>(null)

  const addImage = (img: HTMLImageElement) => {
    if (!imageRefs.value.includes(img)) {
      imageRefs.value.push(img)
      if (observer.value) {
        observer.value.observe(img)
      }
    }
  }

  const removeImage = (img: HTMLImageElement) => {
    const index = imageRefs.value.indexOf(img)
    if (index > -1) {
      imageRefs.value.splice(index, 1)
      if (observer.value) {
        observer.value.unobserve(img)
      }
    }
  }

  const loadImage = (img: HTMLImageElement) => {
    const src = img.dataset.src
    if (src) {
      img.src = src
      img.removeAttribute('data-src')
      img.classList.add('loaded')
    }
  }

  onMounted(() => {
    if ('IntersectionObserver' in window) {
      observer.value = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            loadImage(img)

            if (once) {
              observer.value?.unobserve(img)
              removeImage(img)
            }
          }
        })
      }, {
        root,
        rootMargin,
        threshold
      })

      // 观察已存在的图片
      imageRefs.value.forEach(img => {
        observer.value?.observe(img)
      })
    } else {
      // 不支持IntersectionObserver时直接加载所有图片
      imageRefs.value.forEach(loadImage)
    }
  })

  onUnmounted(() => {
    if (observer.value) {
      observer.value.disconnect()
    }
  })

  return {
    addImage,
    removeImage
  }
}

// 组件懒加载
export function useComponentLazyLoad<T = any>(
  loader: () => Promise<T>,
  options: LazyLoadOptions = {}
) {
  const {
    root = null,
    rootMargin = '100px',
    threshold = 0.1
  } = options

  const isVisible = ref(false)
  const isLoaded = ref(false)
  const component = ref<T | null>(null)
  const error = ref<Error | null>(null)
  const loading = ref(false)
  const elementRef = ref<HTMLElement>()

  const loadComponent = async () => {
    if (isLoaded.value || loading.value) return

    loading.value = true
    error.value = null

    try {
      component.value = await loader()
      isLoaded.value = true
    } catch (err) {
      error.value = err as Error
      console.error('Component lazy load failed:', err)
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    await nextTick()

    if (!elementRef.value) return

    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            isVisible.value = true
            loadComponent()
            observer.unobserve(entry.target)
          }
        })
      }, {
        root,
        rootMargin,
        threshold
      })

      observer.observe(elementRef.value)

      onUnmounted(() => {
        observer.disconnect()
      })
    } else {
      // 不支持IntersectionObserver时直接加载
      isVisible.value = true
      loadComponent()
    }
  })

  return {
    elementRef,
    isVisible,
    isLoaded,
    component,
    error,
    loading,
    loadComponent
  }
}

// 列表懒加载（无限滚动）
export function useInfiniteScroll(
  loadMore: () => Promise<void>,
  options: LazyLoadOptions & {
    distance?: number
    disabled?: () => boolean
  } = {}
) {
  const {
    root = null,
    rootMargin = '100px',
    threshold = 0.1,
    disabled = () => false
  } = options

  const loading = ref(false)
  const elementRef = ref<HTMLElement>()
  const observer = ref<IntersectionObserver | null>(null)

  const load = async () => {
    if (loading.value || disabled()) return

    loading.value = true
    try {
      await loadMore()
    } catch (error) {
      console.error('Infinite scroll load failed:', error)
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    await nextTick()

    if (!elementRef.value) return

    if ('IntersectionObserver' in window) {
      observer.value = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            load()
          }
        })
      }, {
        root,
        rootMargin,
        threshold
      })

      observer.value.observe(elementRef.value)
    }
  })

  onUnmounted(() => {
    if (observer.value) {
      observer.value.disconnect()
    }
  })

  return {
    elementRef,
    loading,
    load
  }
}

// 虚拟滚动
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  buffer = 5
) {
  const scrollTop = ref(0)
  const containerRef = ref<HTMLElement>()

  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight),
      items.length
    )

    return {
      start: Math.max(0, start - buffer),
      end: Math.min(items.length, end + buffer)
    }
  })

  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.slice(start, end).map((item, index) => ({
      item,
      index: start + index
    }))
  })

  const totalHeight = computed(() => items.length * itemHeight)

  const offsetY = computed(() => visibleRange.value.start * itemHeight)

  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }

  onMounted(() => {
    if (containerRef.value) {
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
    }
  })

  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
  })

  return {
    containerRef,
    visibleItems,
    totalHeight,
    offsetY,
    scrollTop
  }
}

// 预加载资源
export function usePreload() {
  const preloadedResources = new Set<string>()

  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (preloadedResources.has(src)) {
        resolve()
        return
      }

      const img = new Image()
      img.onload = () => {
        preloadedResources.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }

  const preloadImages = async (srcs: string[]): Promise<void> => {
    await Promise.all(srcs.map(preloadImage))
  }

  const preloadComponent = async <T>(
    loader: () => Promise<T>
  ): Promise<T> => {
    return loader()
  }

  return {
    preloadImage,
    preloadImages,
    preloadComponent,
    preloadedResources
  }
}

// 性能监控
export function usePerformanceMonitor() {
  const metrics = ref({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0
  })

  const measureLoadTime = () => {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      metrics.value.loadTime = navigation.loadEventEnd - navigation.loadEventStart
    }
  }

  const measureRenderTime = () => {
    if ('performance' in window) {
      const paintEntries = performance.getEntriesByType('paint')
      const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
      if (fcp) {
        metrics.value.renderTime = fcp.startTime
      }
    }
  }

  const measureMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      metrics.value.memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // MB
    }
  }

  const measure = () => {
    measureLoadTime()
    measureRenderTime()
    measureMemoryUsage()
  }

  onMounted(() => {
    // 页面加载完成后测量
    if (document.readyState === 'complete') {
      measure()
    } else {
      window.addEventListener('load', measure)
    }
  })

  return {
    metrics,
    measure
  }
}
