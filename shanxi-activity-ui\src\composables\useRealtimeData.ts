import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRealtime } from '@/services/realtime'
import { useActivityStore, useDrawStore, useUserStore } from '@/stores'
import type { DrawRecord, MarqueeMessage } from '@/types'

export interface RealtimeDataOptions {
  activityId?: number
  enableDrawRecords?: boolean
  enableMarqueeMessages?: boolean
  enableActivityUpdates?: boolean
  enableUserUpdates?: boolean
  autoStart?: boolean
}

export function useRealtimeData(options: RealtimeDataOptions = {}) {
  const {
    activityId,
    enableDrawRecords = true,
    enableMarqueeMessages = true,
    enableActivityUpdates = true,
    enableUserUpdates = true,
    autoStart = true
  } = options

  const { service, connected, onNewDrawRecord, onActivityUpdate, onMarqueeMessage } = useRealtime()
  const activityStore = useActivityStore()
  const drawStore = useDrawStore()
  const userStore = useUserStore()

  // 状态
  const isActive = ref(false)
  const lastUpdateTime = ref<Date | null>(null)
  const updateCount = ref(0)

  // 事件处理器
  const handleNewDrawRecord = (record: DrawRecord) => {
    if (activityId && record.activityId !== activityId) return
    
    console.log('New draw record received:', record)
    lastUpdateTime.value = new Date()
    updateCount.value++
    
    // 更新store
    drawStore.addNewRecord(record)
    
    // 如果是当前用户的记录，更新用户余额
    if (record.isMyRecord && record.rewardType === 'tongbao') {
      userStore.updateTongbaoBalance(
        userStore.tongbaoBalance + (record.tongbaoAmount || 0)
      )
    }
  }

  const handleActivityUpdate = (activityData: any) => {
    if (activityId && activityData.id !== activityId) return
    
    console.log('Activity update received:', activityData)
    lastUpdateTime.value = new Date()
    updateCount.value++
    
    // 更新store
    activityStore.updateActivityInList(activityData)
    
    // 如果是当前活动，更新详情
    if (activityStore.currentActivity?.id === activityData.id) {
      activityStore.updateCurrentActivity(activityData)
    }
  }

  const handleMarqueeMessage = (message: MarqueeMessage) => {
    if (activityId && message.activityId !== activityId) return
    
    console.log('Marquee message received:', message)
    lastUpdateTime.value = new Date()
    updateCount.value++
    
    // 更新store
    drawStore.addNewMarqueeMessage(message)
  }

  const handleUserBalanceUpdate = (balanceData: any) => {
    console.log('User balance update received:', balanceData)
    lastUpdateTime.value = new Date()
    updateCount.value++
    
    // 更新用户余额
    userStore.updateTongbaoBalance(balanceData.balance)
  }

  // 启动实时数据监听
  const start = () => {
    if (isActive.value) return
    
    isActive.value = true
    
    // 注册事件监听器
    if (enableDrawRecords) {
      onNewDrawRecord(handleNewDrawRecord)
    }
    
    if (enableActivityUpdates) {
      onActivityUpdate(handleActivityUpdate)
    }
    
    if (enableMarqueeMessages) {
      onMarqueeMessage(handleMarqueeMessage)
    }
    
    if (enableUserUpdates) {
      service.on('user_balance_update', handleUserBalanceUpdate)
    }
    
    console.log('Realtime data monitoring started')
  }

  // 停止实时数据监听
  const stop = () => {
    if (!isActive.value) return
    
    isActive.value = false
    
    // 移除事件监听器
    service.off('new_draw_record', handleNewDrawRecord)
    service.off('activity_update', handleActivityUpdate)
    service.off('marquee_message', handleMarqueeMessage)
    service.off('user_balance_update', handleUserBalanceUpdate)
    
    console.log('Realtime data monitoring stopped')
  }

  // 手动刷新数据
  const refresh = async () => {
    try {
      if (activityId) {
        // 刷新活动数据
        await activityStore.fetchActivityDetail(activityId)
        
        // 刷新抽奖记录
        await drawStore.fetchDrawRecords({
          activityId,
          page: 1,
          limit: 20,
          type: 'all'
        })
        
        // 刷新跑马灯消息
        await drawStore.fetchMarqueeMessages(activityId)
      }
      
      // 刷新用户信息
      await userStore.fetchUserInfo()
      
      lastUpdateTime.value = new Date()
      console.log('Data refreshed manually')
    } catch (error) {
      console.error('Failed to refresh data:', error)
    }
  }

  // 监听连接状态变化
  watch(connected, (isConnected) => {
    if (isConnected && isActive.value) {
      console.log('Realtime connection restored')
      // 连接恢复后刷新数据
      refresh()
    }
  })

  // 生命周期
  onMounted(() => {
    if (autoStart) {
      start()
    }
  })

  onUnmounted(() => {
    stop()
  })

  return {
    // 状态
    isActive,
    connected,
    lastUpdateTime,
    updateCount,
    
    // 方法
    start,
    stop,
    refresh,
    
    // 便捷方法
    isConnected: connected,
    hasRecentUpdates: () => {
      if (!lastUpdateTime.value) return false
      const now = Date.now()
      const lastUpdate = lastUpdateTime.value.getTime()
      return (now - lastUpdate) < 60000 // 1分钟内有更新
    }
  }
}

// 专门用于活动页面的实时数据
export function useActivityRealtimeData(activityId: number) {
  return useRealtimeData({
    activityId,
    enableDrawRecords: true,
    enableMarqueeMessages: true,
    enableActivityUpdates: true,
    enableUserUpdates: true,
    autoStart: true
  })
}

// 专门用于玩家参与页面的实时数据
export function usePlayerRealtimeData(activityId: number) {
  return useRealtimeData({
    activityId,
    enableDrawRecords: true,
    enableMarqueeMessages: true,
    enableActivityUpdates: false,
    enableUserUpdates: true,
    autoStart: true
  })
}

// 专门用于管理页面的实时数据
export function useManagementRealtimeData() {
  return useRealtimeData({
    enableDrawRecords: false,
    enableMarqueeMessages: false,
    enableActivityUpdates: true,
    enableUserUpdates: false,
    autoStart: true
  })
}
