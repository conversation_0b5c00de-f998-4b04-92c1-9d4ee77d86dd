<template>
  <div class="error-boundary">
    <slot v-if="!hasError" />
    
    <!-- 错误显示 -->
    <div v-else class="error-container">
      <el-result
        :icon="errorIcon"
        :title="errorTitle"
        :sub-title="errorMessage"
      >
        <template #extra>
          <div class="error-actions">
            <el-button type="primary" @click="handleRetry">
              <el-icon><Refresh /></el-icon>
              重试
            </el-button>
            
            <el-button @click="handleGoBack">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            
            <el-button 
              v-if="showReportButton"
              type="danger" 
              @click="handleReportError"
            >
              <el-icon><Warning /></el-icon>
              报告错误
            </el-button>
          </div>
          
          <!-- 错误详情（开发环境） -->
          <div v-if="showErrorDetails" class="error-details">
            <el-collapse>
              <el-collapse-item title="错误详情" name="details">
                <pre class="error-stack">{{ errorDetails }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onErrorCaptured, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Refresh, 
  ArrowLeft, 
  Warning 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Props {
  fallbackComponent?: string
  showReportButton?: boolean
  onError?: (error: Error, instance: any, info: string) => void
}

const props = withDefaults(defineProps<Props>(), {
  showReportButton: true
})

const emit = defineEmits<{
  error: [error: Error, info: string]
  retry: []
}>()

const router = useRouter()

// 状态
const hasError = ref(false)
const errorInfo = ref<{
  error: Error | null
  info: string
  timestamp: number
}>({
  error: null,
  info: '',
  timestamp: 0
})

// 计算属性
const errorIcon = computed(() => {
  if (!errorInfo.value.error) return 'warning'
  
  const error = errorInfo.value.error
  if (error.name === 'ChunkLoadError') return 'warning'
  if (error.message.includes('Network')) return 'warning'
  return 'error'
})

const errorTitle = computed(() => {
  if (!errorInfo.value.error) return '出现错误'
  
  const error = errorInfo.value.error
  if (error.name === 'ChunkLoadError') return '资源加载失败'
  if (error.message.includes('Network')) return '网络连接失败'
  if (error.message.includes('Permission')) return '权限不足'
  return '系统错误'
})

const errorMessage = computed(() => {
  if (!errorInfo.value.error) return '页面出现了未知错误'
  
  const error = errorInfo.value.error
  if (error.name === 'ChunkLoadError') {
    return '页面资源加载失败，可能是网络问题或版本更新导致'
  }
  if (error.message.includes('Network')) {
    return '网络连接失败，请检查网络设置后重试'
  }
  if (error.message.includes('Permission')) {
    return '您没有访问该功能的权限，请联系管理员'
  }
  return error.message || '页面出现了未知错误，请稍后重试'
})

const errorDetails = computed(() => {
  if (!errorInfo.value.error) return ''
  
  return `错误类型: ${errorInfo.value.error.name}
错误信息: ${errorInfo.value.error.message}
发生时间: ${new Date(errorInfo.value.timestamp).toLocaleString()}
错误位置: ${errorInfo.value.info}

堆栈信息:
${errorInfo.value.error.stack || '无堆栈信息'}`
})

const showErrorDetails = computed(() => {
  return import.meta.env.DEV && errorInfo.value.error
})

// 错误捕获
onErrorCaptured((error: Error, instance: any, info: string) => {
  console.error('Error captured by ErrorBoundary:', error, info)
  
  hasError.value = true
  errorInfo.value = {
    error,
    info,
    timestamp: Date.now()
  }
  
  // 调用外部错误处理器
  if (props.onError) {
    props.onError(error, instance, info)
  }
  
  // 触发错误事件
  emit('error', error, info)
  
  // 阻止错误继续向上传播
  return false
})

// 全局错误监听
onMounted(() => {
  // 监听未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    hasError.value = true
    errorInfo.value = {
      error: new Error(event.reason?.message || 'Promise rejection'),
      info: 'unhandledrejection',
      timestamp: Date.now()
    }
  })
  
  // 监听JavaScript错误
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    hasError.value = true
    errorInfo.value = {
      error: event.error || new Error(event.message),
      info: `${event.filename}:${event.lineno}:${event.colno}`,
      timestamp: Date.now()
    }
  })
})

// 方法
const handleRetry = () => {
  hasError.value = false
  errorInfo.value = {
    error: null,
    info: '',
    timestamp: 0
  }
  
  emit('retry')
  
  // 如果是资源加载错误，刷新页面
  if (errorInfo.value.error?.name === 'ChunkLoadError') {
    window.location.reload()
  }
}

const handleGoBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}

const handleReportError = async () => {
  try {
    // 这里可以实现错误报告功能
    // 比如发送到错误监控服务
    const errorReport = {
      error: {
        name: errorInfo.value.error?.name,
        message: errorInfo.value.error?.message,
        stack: errorInfo.value.error?.stack
      },
      info: errorInfo.value.info,
      timestamp: errorInfo.value.timestamp,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: 'current-user-id' // 从用户store获取
    }
    
    console.log('Error report:', errorReport)
    
    // 模拟发送错误报告
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('错误报告已发送，感谢您的反馈！')
  } catch (error) {
    console.error('Failed to report error:', error)
    ElMessage.error('错误报告发送失败')
  }
}

// 重置错误状态
const resetError = () => {
  hasError.value = false
  errorInfo.value = {
    error: null,
    info: '',
    timestamp: 0
  }
}

// 暴露方法
defineExpose({
  resetError,
  hasError: computed(() => hasError.value),
  errorInfo: computed(() => errorInfo.value)
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.error-boundary {
  width: 100%;
  height: 100%;
  
  .error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: $spacing-xl;
    
    .error-actions {
      display: flex;
      gap: $spacing-md;
      justify-content: center;
      margin-bottom: $spacing-lg;
    }
    
    .error-details {
      margin-top: $spacing-lg;
      max-width: 600px;
      
      .error-stack {
        background: $background-color-light;
        padding: $spacing-md;
        border-radius: $border-radius-base;
        font-size: $font-size-sm;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 300px;
        overflow-y: auto;
      }
    }
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .error-boundary {
    .error-container {
      padding: $spacing-md;
      
      .error-actions {
        flex-direction: column;
        
        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
