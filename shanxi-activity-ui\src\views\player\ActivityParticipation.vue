<template>
  <div class="activity-participation">
    <LoadingSpinner v-if="loading" text="加载活动信息..." />
    
    <div v-else-if="activity" class="participation-content">
      <!-- 活动信息卡片 -->
      <BaseCard class="activity-info-card">
        <template #header>
          <div class="activity-header">
            <h2>{{ activity.activityName }}</h2>
            <StatusTag :status="activity.status" />
          </div>
        </template>
        
        <div class="activity-summary">
          <el-row :gutter="24">
            <el-col :span="6">
              <el-statistic 
                title="剩余通宝" 
                :value="activity.remainingTongbao"
                :formatter="formatTongbao"
                :value-style="{ color: '#52c41a' }"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic 
                title="参与人数" 
                :value="activity.participantsCount"
                :value-style="{ color: '#1890ff' }"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic 
                title="我的抽奖次数" 
                :value="drawChances?.remainingChances || 0"
                :value-style="{ color: '#fa8c16' }"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic 
                title="活动倒计时" 
                :value="countdown"
                :value-style="{ color: '#722ed1' }"
              />
            </el-col>
          </el-row>
        </div>
        
        <!-- 活动进度条 -->
        <div class="activity-progress">
          <div class="progress-info">
            <span>活动进度</span>
            <span>{{ progressPercentage }}%</span>
          </div>
          <el-progress 
            :percentage="progressPercentage" 
            :color="getProgressColor(progressPercentage)"
            :stroke-width="8"
          />
        </div>
      </BaseCard>
      
      <!-- 抽奖区域 -->
      <BaseCard title="盲盒抽奖" class="lottery-card">
        <template #extra>
          <el-button 
            @click="showRewardsList = true"
            size="small"
          >
            查看奖品
          </el-button>
        </template>
        
        <div class="lottery-section">
          <div class="lottery-box-container">
            <div 
              class="lottery-box"
              :class="{ 
                'is-drawing': isDrawing,
                'can-draw': canDraw
              }"
              @click="handleDraw"
            >
              <div class="box-content">
                <el-icon class="box-icon" size="48">
                  <Gift />
                </el-icon>
                <div class="box-text">
                  {{ getBoxText() }}
                </div>
              </div>
            </div>
          </div>
          
          <div class="lottery-info">
            <div class="chances-info">
              <span class="label">剩余次数：</span>
              <span class="value">{{ drawChances?.remainingChances || 0 }}</span>
            </div>
            <div class="total-info">
              <span class="label">总次数：</span>
              <span class="value">{{ drawChances?.totalChances || 0 }}</span>
            </div>
          </div>
        </div>
      </BaseCard>
      
      <!-- 任务列表 -->
      <BaseCard title="任务列表" class="tasks-card">
        <div class="tasks-container">
          <div 
            v-for="task in activity.tasks" 
            :key="task.id"
            class="task-item"
          >
            <TaskCard 
              :task="task"
              @complete="handleTaskComplete"
            />
          </div>
        </div>
      </BaseCard>
      
      <!-- 中奖记录 -->
      <BaseCard title="中奖记录" class="records-card">
        <template #extra>
          <el-radio-group v-model="recordType" size="small">
            <el-radio-button label="my">我的记录</el-radio-button>
            <el-radio-button label="all">全部记录</el-radio-button>
          </el-radio-group>
        </template>
        
        <DrawRecordsList 
          :activity-id="activityId"
          :type="recordType"
          :limit="10"
        />
      </BaseCard>
      
      <!-- 跑马灯 -->
      <MarqueeMessages :activity-id="activityId" />
    </div>
    
    <EmptyState 
      v-else 
      type="error" 
      title="活动不存在"
      description="该活动可能已结束或您没有参与权限"
    >
      <template #actions>
        <el-button @click="$router.back()">返回</el-button>
      </template>
    </EmptyState>
    
    <!-- 抽奖结果弹窗 -->
    <DrawResultDialog 
      v-model="showDrawResult"
      :result="lastDrawResult"
      @close="handleDrawResultClose"
    />
    
    <!-- 奖品列表弹窗 -->
    <RewardsListDialog 
      v-model="showRewardsList"
      :rewards="activity?.rewards || []"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { Gift } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { 
  BaseCard, 
  StatusTag, 
  LoadingSpinner, 
  EmptyState 
} from '@/components/common'
import TaskCard from './components/TaskCard.vue'
import DrawRecordsList from './components/DrawRecordsList.vue'
import MarqueeMessages from './components/MarqueeMessages.vue'
import DrawResultDialog from './components/DrawResultDialog.vue'
import RewardsListDialog from './components/RewardsListDialog.vue'
import { useActivityStore, useDrawStore } from '@/stores'
import { formatTongbao, formatCountdown } from '@/utils'

const route = useRoute()
const activityStore = useActivityStore()
const drawStore = useDrawStore()

const loading = ref(false)
const isDrawing = ref(false)
const recordType = ref<'my' | 'all'>('my')
const showDrawResult = ref(false)
const showRewardsList = ref(false)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

const activityId = Number(route.params.id)

// 计算属性
const activity = computed(() => activityStore.currentActivity)
const drawChances = computed(() => drawStore.drawChances)
const lastDrawResult = computed(() => drawStore.lastDrawResult)

const canDraw = computed(() => {
  return activity.value?.status === 'running' && 
         drawChances.value && 
         drawChances.value.remainingChances > 0 && 
         !isDrawing.value
})

const progressPercentage = computed(() => {
  if (!activity.value) return 0
  const used = activity.value.totalTongbao - activity.value.remainingTongbao
  return Math.round((used / activity.value.totalTongbao) * 100)
})

const countdown = computed(() => {
  if (!activity.value) return '已结束'
  const now = Date.now()
  const endTime = new Date(activity.value.endTime).getTime()
  const remaining = Math.max(0, endTime - now)
  return formatCountdown(Math.floor(remaining / 1000))
})

// 方法
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#67C23A'
  if (percentage < 70) return '#E6A23C'
  return '#F56C6C'
}

const getBoxText = () => {
  if (isDrawing.value) return '抽奖中...'
  if (!canDraw.value) return '无法抽奖'
  return '点击抽奖'
}

const loadActivityData = async () => {
  loading.value = true
  try {
    await Promise.all([
      activityStore.fetchActivityDetail(activityId),
      drawStore.fetchDrawChances(activityId)
    ])
  } catch (error) {
    console.error('Failed to load activity data:', error)
    ElMessage.error('加载活动数据失败')
  } finally {
    loading.value = false
  }
}

const handleDraw = async () => {
  if (!canDraw.value) return
  
  isDrawing.value = true
  try {
    await drawStore.executeDrawReward(activityId)
    showDrawResult.value = true
  } catch (error) {
    console.error('Failed to draw reward:', error)
    ElMessage.error('抽奖失败，请重试')
  } finally {
    isDrawing.value = false
  }
}

const handleDrawResultClose = () => {
  showDrawResult.value = false
  drawStore.clearLastDrawResult()
}

const handleTaskComplete = (taskId: number) => {
  // 刷新抽奖次数
  drawStore.fetchDrawChances(activityId)
  ElMessage.success('任务完成，获得抽奖次数！')
}

const startCountdownTimer = () => {
  countdownTimer.value = setInterval(() => {
    // 触发倒计时更新
  }, 1000)
}

const stopCountdownTimer = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  loadActivityData()
  startCountdownTimer()
})

onUnmounted(() => {
  stopCountdownTimer()
  drawStore.clearDrawData()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.activity-participation {
  .participation-content {
    .activity-info-card {
      margin-bottom: $spacing-lg;
      
      .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h2 {
          margin: 0;
          color: $text-color-primary;
        }
      }
      
      .activity-summary {
        margin-bottom: $spacing-lg;
      }
      
      .activity-progress {
        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: $spacing-sm;
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }
    }
    
    .lottery-card {
      margin-bottom: $spacing-lg;
      
      .lottery-section {
        display: flex;
        align-items: center;
        gap: $spacing-xl;
        
        .lottery-box-container {
          flex: 1;
          display: flex;
          justify-content: center;
          
          .lottery-box {
            width: 120px;
            height: 120px;
            border: 3px solid $border-color-base;
            border-radius: $border-radius-lg;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
            
            &.can-draw {
              border-color: $primary-color;
              background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
              color: white;
              
              &:hover {
                transform: scale(1.05);
                box-shadow: 0 4px 12px rgba($primary-color, 0.3);
              }
            }
            
            &.is-drawing {
              animation: shake 0.5s infinite;
              border-color: $warning-color;
              background: linear-gradient(135deg, $warning-color 0%, lighten($warning-color, 10%) 100%);
              color: white;
            }
            
            .box-content {
              text-align: center;
              
              .box-icon {
                margin-bottom: $spacing-sm;
              }
              
              .box-text {
                font-size: $font-size-sm;
                font-weight: 600;
              }
            }
          }
        }
        
        .lottery-info {
          flex: 1;
          
          .chances-info,
          .total-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: $spacing-md;
            
            .label {
              color: $text-color-secondary;
            }
            
            .value {
              color: $text-color-primary;
              font-weight: 600;
            }
          }
        }
      }
    }
    
    .tasks-card,
    .records-card {
      margin-bottom: $spacing-lg;
    }
    
    .tasks-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: $spacing-md;
    }
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

// 移动端适配
@media (max-width: $screen-md) {
  .activity-participation {
    .lottery-section {
      flex-direction: column;
      gap: $spacing-lg;
      
      .lottery-box-container,
      .lottery-info {
        width: 100%;
      }
    }
    
    .tasks-container {
      grid-template-columns: 1fr;
    }
  }
}
</style>
