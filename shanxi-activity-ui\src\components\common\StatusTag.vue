<template>
  <el-tag
    :type="tagType"
    :size="size"
    :effect="effect"
    :class="['status-tag', `status-${status}`]"
  >
    <el-icon v-if="showIcon" class="status-icon">
      <component :is="statusIcon" />
    </el-icon>
    {{ statusText }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Clock, 
  VideoPlay, 
  CircleCheck, 
  CircleClose,
  Warning,
  InfoFilled
} from '@element-plus/icons-vue'
import type { ActivityStatus, UserRole } from '@/types'

interface Props {
  status: ActivityStatus | UserRole | string
  size?: 'large' | 'default' | 'small'
  effect?: 'dark' | 'light' | 'plain'
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default',
  effect: 'light',
  showIcon: true
})

// 状态映射配置
const statusConfig = {
  // 活动状态
  not_started: { type: 'info', text: '未开始', icon: Clock },
  running: { type: 'success', text: '进行中', icon: VideoPlay },
  ended: { type: 'warning', text: '已结束', icon: Warning },
  closed: { type: 'danger', text: '已关闭', icon: CircleClose },
  
  // 用户角色
  alliance_leader: { type: 'danger', text: '盟主', icon: InfoFilled },
  partner: { type: 'warning', text: '合伙人', icon: InfoFilled },
  player: { type: 'primary', text: '玩家', icon: InfoFilled },
  
  // 任务状态
  completed: { type: 'success', text: '已完成', icon: CircleCheck },
  in_progress: { type: 'primary', text: '进行中', icon: VideoPlay },
  pending: { type: 'info', text: '待开始', icon: Clock },
  
  // 通用状态
  active: { type: 'success', text: '激活', icon: CircleCheck },
  inactive: { type: 'info', text: '未激活', icon: CircleClose },
  success: { type: 'success', text: '成功', icon: CircleCheck },
  error: { type: 'danger', text: '错误', icon: CircleClose },
  warning: { type: 'warning', text: '警告', icon: Warning },
  info: { type: 'info', text: '信息', icon: InfoFilled }
}

const config = computed(() => {
  return statusConfig[props.status as keyof typeof statusConfig] || {
    type: 'info',
    text: props.status,
    icon: InfoFilled
  }
})

const tagType = computed(() => config.value.type as any)
const statusText = computed(() => config.value.text)
const statusIcon = computed(() => config.value.icon)
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.status-tag {
  display: inline-flex;
  align-items: center;
  gap: $spacing-xs;
  
  .status-icon {
    font-size: 12px;
  }
  
  &.status-not_started {
    --el-tag-bg-color: #{lighten($info-color, 45%)};
    --el-tag-border-color: #{lighten($info-color, 25%)};
    --el-tag-text-color: #{$info-color};
  }
  
  &.status-running {
    --el-tag-bg-color: #{lighten($success-color, 45%)};
    --el-tag-border-color: #{lighten($success-color, 25%)};
    --el-tag-text-color: #{$success-color};
  }
  
  &.status-ended {
    --el-tag-bg-color: #{lighten($warning-color, 45%)};
    --el-tag-border-color: #{lighten($warning-color, 25%)};
    --el-tag-text-color: #{$warning-color};
  }
  
  &.status-closed {
    --el-tag-bg-color: #{lighten($error-color, 45%)};
    --el-tag-border-color: #{lighten($error-color, 25%)};
    --el-tag-text-color: #{$error-color};
  }
}
</style>
