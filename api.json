[{"name": "Activity", "apiCount": 4, "apis": [{"path": "/api/activities", "method": "POST", "summary": "创建活动", "req": {"_entity": "CreateActivityDto", "activityName": "string", "activityType": "object", "totalTongbao": "number", "startTime": "string", "endTime": "string", "rewards": [{"rewardName": "string", "rewardType": "object", "tongbaoAmount": "number", "physicalItem": "string", "weight": "integer", "dailyQuantity": "integer"}], "tasks": [{"taskType": "object", "taskName": "string", "targetValue": "integer", "rewardChances": "integer", "refreshType": "object"}]}, "res": {"200": "Success", "_entity": "ActivityResponseDtoResult", "code": "integer", "msg": "string", "success": "boolean", "data": {"id": "integer", "activityName": "string", "activityType": "object", "totalTongbao": "number", "remainingTongbao": "number", "startTime": "string", "endTime": "string", "status": "object", "participantsCount": "integer", "creatorNickname": "string"}, "_dataEntity": "ActivityResponseDto"}}, {"path": "/api/activities", "method": "GET", "summary": "获取活动列表", "req": {"status": "object", "scope": "string", "page": "integer", "limit": "integer"}, "res": {"200": "Success"}}, {"path": "/api/activities/{activityId}", "method": "GET", "summary": "获取活动详情", "req": {"activityId": "integer*"}, "res": {"200": "Success", "_entity": "ActivityDetailResponseDtoResult", "code": "integer", "msg": "string", "success": "boolean", "data": {"id": "integer", "activityName": "string", "activityType": "object", "totalTongbao": "number", "remainingTongbao": "number", "startTime": "string", "endTime": "string", "status": "object", "participantsCount": "integer", "creatorNickname": "string", "rewards": [{"id": "integer", "rewardName": "string", "rewardType": "object", "tongbaoAmount": "number", "physicalItem": "string", "probability": "number", "remainingQuantity": "integer"}], "tasks": [{"id": "integer", "taskType": "object", "taskName": "string", "targetValue": "integer", "rewardChances": "integer", "refreshType": "object"}]}, "_dataEntity": "ActivityDetailResponseDto"}}, {"path": "/api/activities/{activityId}/close", "method": "PUT", "summary": "关闭活动", "req": {"activityId": "integer*"}, "res": {"200": "Success"}}]}, {"name": "Draw", "apiCount": 4, "apis": [{"path": "/api/activities/{activityId}/draw-chances", "method": "GET", "summary": "获取抽奖次数", "req": {"activityId": "integer*"}, "res": {"200": "Success", "_entity": "DrawChancesResponseDtoResult", "code": "integer", "msg": "string", "success": "boolean", "data": {"totalChances": "integer", "usedChances": "integer", "remainingChances": "integer"}, "_dataEntity": "DrawChancesResponseDto"}}, {"path": "/api/activities/{activityId}/draw", "method": "POST", "summary": "执行抽奖", "req": {"activityId": "integer*", "_entity": "DrawRewardDto", "id": "integer", "rewardName": "string", "rewardType": "object", "tongbaoAmount": "number", "physicalItem": "string"}, "res": {"200": "Success", "_entity": "DrawResultResponseDtoResult", "code": "integer", "msg": "string", "success": "boolean", "data": {"drawId": "integer", "reward": "object", "remainingChances": "integer"}, "_dataEntity": "DrawResultResponseDto"}}, {"path": "/api/activities/{activityId}/draw-records", "method": "GET", "summary": "获取抽奖记录", "req": {"activityId": "integer*", "type": "string", "limit": "integer"}, "res": {"200": "Success"}}, {"path": "/api/activities/{activityId}/add-chances", "method": "POST", "summary": "增加抽奖次数（内部接口，由任务系统调用）", "req": {"activityId": "integer*", "gameUserId": "string", "playerNickname": "string", "chances": "integer"}, "res": {"200": "Success", "_entity": "DrawChancesResponseDtoResult", "code": "integer", "msg": "string", "success": "boolean", "data": {"totalChances": "integer", "usedChances": "integer", "remainingChances": "integer"}, "_dataEntity": "DrawChancesResponseDto"}}]}, {"name": "UserCache", "apiCount": 3, "apis": [{"path": "/api/users/cache-nickname", "method": "POST", "summary": "缓存用户昵称", "req": {"_entity": "CacheUserNicknameDto", "gameUserId": "string", "nickname": "string"}, "res": {"200": "Success", "_entity": "UserCacheResponseDtoResult", "code": "integer", "msg": "string", "success": "boolean", "data": {"gameUserId": "string", "nickname": "string"}, "_dataEntity": "UserCacheResponseDto"}}, {"path": "/api/users/{gameUserId}/nickname", "method": "GET", "summary": "获取用户昵称", "req": {"gameUserId": "string*"}, "res": {"200": "Success", "_entity": "StringResult", "code": "integer", "msg": "string", "success": "boolean", "data": "string"}}, {"path": "/api/users/nicknames", "method": "POST", "summary": "批量获取用户昵称", "req": {}, "res": {"200": "Success", "_entity": "StringStringDictionaryResult", "code": "integer", "msg": "string", "success": "boolean", "data": "object"}}]}]