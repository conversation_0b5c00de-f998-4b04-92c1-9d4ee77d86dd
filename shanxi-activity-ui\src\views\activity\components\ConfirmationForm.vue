<template>
  <div class="confirmation-form">
    <div class="form-header">
      <h3>确认活动信息</h3>
      <div class="form-tip">
        请仔细检查以下信息，确认无误后点击创建活动
      </div>
    </div>
    
    <!-- 基础信息 -->
    <el-card class="info-section">
      <template #header>
        <h4>基础信息</h4>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="活动名称">
          {{ formData.basicInfo.activityName }}
        </el-descriptions-item>
        <el-descriptions-item label="活动类型">
          {{ getActivityTypeText(formData.basicInfo.activityType) }}
        </el-descriptions-item>
        <el-descriptions-item label="总通宝数量">
          {{ formatTongbao(formData.basicInfo.totalTongbao) }}
        </el-descriptions-item>
        <el-descriptions-item label="活动时长">
          {{ getActivityDuration() }}
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">
          {{ formatDateTime(formData.basicInfo.startTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="结束时间">
          {{ formatDateTime(formData.basicInfo.endTime) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 奖励配置 -->
    <el-card class="info-section">
      <template #header>
        <h4>奖励配置 ({{ formData.rewards.length }} 个奖励)</h4>
      </template>
      
      <el-table :data="formData.rewards" border>
        <el-table-column prop="rewardName" label="奖励名称" />
        <el-table-column prop="rewardType" label="类型">
          <template #default="{ row }">
            <StatusTag :status="row.rewardType === 'tongbao' ? 'success' : 'warning'">
              {{ row.rewardType === 'tongbao' ? '通宝' : '实物' }}
            </StatusTag>
          </template>
        </el-table-column>
        <el-table-column prop="tongbaoAmount" label="通宝数量">
          <template #default="{ row }">
            {{ formatTongbao(row.tongbaoAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="probability" label="中奖概率">
          <template #default="{ row }">
            {{ row.probability?.toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column prop="dailyQuantity" label="每日数量" />
      </el-table>
      
      <div class="summary-info">
        <el-statistic 
          title="总分配通宝" 
          :value="totalAllocatedTongbao"
          :formatter="formatTongbao"
        />
        <el-statistic 
          title="分配比例" 
          :value="allocationRatio"
          suffix="%"
        />
      </div>
    </el-card>
    
    <!-- 任务设置 -->
    <el-card class="info-section">
      <template #header>
        <h4>任务设置 ({{ formData.tasks.length }} 个任务)</h4>
      </template>
      
      <el-table :data="formData.tasks" border>
        <el-table-column prop="taskName" label="任务名称" />
        <el-table-column prop="taskType" label="任务类型">
          <template #default="{ row }">
            {{ getTaskTypeText(row.taskType) }}
          </template>
        </el-table-column>
        <el-table-column prop="targetValue" label="目标值" />
        <el-table-column prop="rewardChances" label="奖励次数" />
        <el-table-column prop="refreshType" label="刷新类型">
          <template #default="{ row }">
            {{ getRefreshTypeText(row.refreshType) }}
          </template>
        </el-table-column>
      </el-table>
      
      <div class="summary-info">
        <el-statistic 
          title="每日最大抽奖次数" 
          :value="maxDailyChances"
        />
      </div>
    </el-card>
    
    <!-- 风险提示 -->
    <el-alert
      title="创建提示"
      type="info"
      show-icon
      :closable="false"
    >
      <template #default>
        <ul class="risk-tips">
          <li>活动创建后，基础信息和时间不可修改</li>
          <li>奖励配置和任务设置可在活动开始前修改</li>
          <li>活动开始后，所有配置将锁定不可修改</li>
          <li>请确保通宝余额充足，系统将在活动开始时扣除相应通宝</li>
        </ul>
      </template>
    </el-alert>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { StatusTag } from '@/components/common'
import { 
  formatTongbao, 
  formatDateTime, 
  getTaskTypeText, 
  getRefreshTypeText 
} from '@/utils'

interface FormData {
  basicInfo: {
    activityName: string
    activityType: string
    totalTongbao: number
    startTime: string
    endTime: string
  }
  rewards: any[]
  tasks: any[]
}

const props = defineProps<{
  formData: FormData
}>()

const emit = defineEmits<{
  create: []
}>()

// 获取活动类型文本
const getActivityTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    blind_box: '盲盒活动'
  }
  return typeMap[type] || type
}

// 计算活动时长
const getActivityDuration = () => {
  const start = new Date(props.formData.basicInfo.startTime)
  const end = new Date(props.formData.basicInfo.endTime)
  const diffTime = end.getTime() - start.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return `${diffDays} 天`
}

// 计算总分配通宝
const totalAllocatedTongbao = computed(() => {
  return props.formData.rewards.reduce((total, reward) => {
    return total + (reward.tongbaoAmount || 0) * (reward.dailyQuantity || 0)
  }, 0)
})

// 计算分配比例
const allocationRatio = computed(() => {
  const ratio = (totalAllocatedTongbao.value / props.formData.basicInfo.totalTongbao) * 100
  return Math.round(ratio * 100) / 100
})

// 计算每日最大抽奖次数
const maxDailyChances = computed(() => {
  return props.formData.tasks.reduce((total, task) => {
    if (task.refreshType === 'daily') {
      return total + (task.rewardChances || 0)
    }
    return total
  }, 0)
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.confirmation-form {
  .form-header {
    margin-bottom: $spacing-lg;
    
    h3 {
      margin: 0 0 $spacing-sm 0;
      color: $text-color-primary;
    }
    
    .form-tip {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }
  
  .info-section {
    margin-bottom: $spacing-lg;
    
    h4 {
      margin: 0;
      color: $text-color-primary;
      font-size: $font-size-lg;
    }
    
    .summary-info {
      display: flex;
      gap: $spacing-xl;
      margin-top: $spacing-lg;
      padding-top: $spacing-lg;
      border-top: 1px solid $border-color-light;
    }
  }
  
  .risk-tips {
    margin: 0;
    padding-left: $spacing-lg;
    
    li {
      margin-bottom: $spacing-xs;
      color: $text-color-secondary;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .confirmation-form {
    .info-section {
      .summary-info {
        flex-direction: column;
        gap: $spacing-md;
      }
    }
    
    .el-table {
      font-size: $font-size-sm;
    }
  }
}
</style>
