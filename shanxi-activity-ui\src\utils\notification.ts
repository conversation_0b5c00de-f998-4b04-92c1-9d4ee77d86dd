import { ElMessage, ElNotification, ElMessageBox, ElLoading } from 'element-plus'
import type { MessageOptions, NotificationOptions, MessageBoxOptions } from 'element-plus'

// 消息类型
export type MessageType = 'success' | 'warning' | 'info' | 'error'

// 通知配置
interface NotificationConfig {
  title?: string
  message: string
  type?: MessageType
  duration?: number
  showClose?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

// 消息配置
interface MessageConfig {
  message: string
  type?: MessageType
  duration?: number
  showClose?: boolean
  center?: boolean
}

// 确认对话框配置
interface ConfirmConfig {
  title?: string
  message: string
  type?: MessageType
  confirmButtonText?: string
  cancelButtonText?: string
  showCancelButton?: boolean
}

// 加载配置
interface LoadingConfig {
  text?: string
  spinner?: string
  background?: string
  target?: string | HTMLElement
}

class NotificationService {
  private loadingInstance: any = null

  // 成功消息
  success(message: string, options?: Partial<MessageConfig>) {
    return ElMessage.success({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  // 错误消息
  error(message: string, options?: Partial<MessageConfig>) {
    return ElMessage.error({
      message,
      duration: 5000,
      showClose: true,
      ...options
    })
  }

  // 警告消息
  warning(message: string, options?: Partial<MessageConfig>) {
    return ElMessage.warning({
      message,
      duration: 4000,
      showClose: true,
      ...options
    })
  }

  // 信息消息
  info(message: string, options?: Partial<MessageConfig>) {
    return ElMessage.info({
      message,
      duration: 3000,
      showClose: true,
      ...options
    })
  }

  // 通用消息
  message(config: MessageConfig) {
    return ElMessage({
      type: 'info',
      duration: 3000,
      showClose: true,
      ...config
    })
  }

  // 成功通知
  notifySuccess(config: NotificationConfig) {
    return ElNotification.success({
      title: '成功',
      duration: 4000,
      position: 'top-right',
      ...config,
      type: 'success'
    })
  }

  // 错误通知
  notifyError(config: NotificationConfig) {
    return ElNotification.error({
      title: '错误',
      duration: 6000,
      position: 'top-right',
      ...config,
      type: 'error'
    })
  }

  // 警告通知
  notifyWarning(config: NotificationConfig) {
    return ElNotification.warning({
      title: '警告',
      duration: 5000,
      position: 'top-right',
      ...config,
      type: 'warning'
    })
  }

  // 信息通知
  notifyInfo(config: NotificationConfig) {
    return ElNotification.info({
      title: '提示',
      duration: 4000,
      position: 'top-right',
      ...config,
      type: 'info'
    })
  }

  // 通用通知
  notify(config: NotificationConfig) {
    return ElNotification({
      title: '通知',
      duration: 4000,
      position: 'top-right',
      type: 'info',
      ...config
    })
  }

  // 确认对话框
  async confirm(config: ConfirmConfig): Promise<boolean> {
    try {
      await ElMessageBox.confirm(
        config.message,
        config.title || '确认',
        {
          confirmButtonText: config.confirmButtonText || '确定',
          cancelButtonText: config.cancelButtonText || '取消',
          type: config.type || 'warning',
          showCancelButton: config.showCancelButton !== false
        }
      )
      return true
    } catch {
      return false
    }
  }

  // 提示对话框
  async alert(message: string, title?: string, type?: MessageType): Promise<void> {
    return ElMessageBox.alert(message, title || '提示', {
      type: type || 'info',
      confirmButtonText: '确定'
    })
  }

  // 输入对话框
  async prompt(message: string, title?: string, inputValue?: string): Promise<string | null> {
    try {
      const { value } = await ElMessageBox.prompt(message, title || '输入', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue
      })
      return value
    } catch {
      return null
    }
  }

  // 显示加载
  showLoading(config?: LoadingConfig) {
    this.hideLoading() // 先隐藏之前的加载
    
    this.loadingInstance = ElLoading.service({
      text: config?.text || '加载中...',
      spinner: config?.spinner,
      background: config?.background || 'rgba(0, 0, 0, 0.7)',
      target: config?.target
    })
    
    return this.loadingInstance
  }

  // 隐藏加载
  hideLoading() {
    if (this.loadingInstance) {
      this.loadingInstance.close()
      this.loadingInstance = null
    }
  }

  // 关闭所有消息
  closeAllMessages() {
    ElMessage.closeAll()
  }

  // 关闭所有通知
  closeAllNotifications() {
    ElNotification.closeAll()
  }
}

// 创建全局实例
export const notification = new NotificationService()

// 便捷方法
export const {
  success,
  error,
  warning,
  info,
  message,
  notifySuccess,
  notifyError,
  notifyWarning,
  notifyInfo,
  notify,
  confirm,
  alert,
  prompt,
  showLoading,
  hideLoading,
  closeAllMessages,
  closeAllNotifications
} = notification

// 业务相关的通知方法
export const businessNotification = {
  // 活动相关
  activityCreated: (activityName: string) => {
    notifySuccess({
      title: '活动创建成功',
      message: `活动"${activityName}"已成功创建`
    })
  },

  activityClosed: (activityName: string) => {
    notifyWarning({
      title: '活动已关闭',
      message: `活动"${activityName}"已关闭`
    })
  },

  // 抽奖相关
  drawSuccess: (rewardName: string, amount?: number) => {
    const message = amount 
      ? `恭喜您获得 ${rewardName} ${amount}通宝！`
      : `恭喜您获得 ${rewardName}！`
    
    notifySuccess({
      title: '中奖了！',
      message,
      duration: 8000
    })
  },

  drawFailed: (reason?: string) => {
    error(reason || '抽奖失败，请重试')
  },

  noChancesLeft: () => {
    warning('今日抽奖次数已用完，明天再来吧！')
  },

  // 任务相关
  taskCompleted: (taskName: string, rewardChances: number) => {
    success(`任务"${taskName}"完成，获得${rewardChances}次抽奖机会！`)
  },

  // 权限相关
  noPermission: (action?: string) => {
    error(action ? `您没有权限执行"${action}"操作` : '权限不足')
  },

  loginRequired: () => {
    warning('请先登录')
  },

  // 网络相关
  networkError: () => {
    error('网络连接失败，请检查网络设置')
  },

  serverError: () => {
    error('服务器错误，请稍后重试')
  },

  // 数据相关
  dataLoadFailed: (dataType?: string) => {
    error(dataType ? `${dataType}加载失败` : '数据加载失败')
  },

  dataSaved: (dataType?: string) => {
    success(dataType ? `${dataType}保存成功` : '保存成功')
  },

  dataDeleted: (dataType?: string) => {
    success(dataType ? `${dataType}删除成功` : '删除成功')
  }
}

// 错误处理器
export class ErrorHandler {
  static handle(error: any, context?: string) {
    console.error('Error occurred:', error, context)
    
    if (error.response) {
      // HTTP错误
      const status = error.response.status
      const message = error.response.data?.message || error.message
      
      switch (status) {
        case 401:
          businessNotification.loginRequired()
          break
        case 403:
          businessNotification.noPermission()
          break
        case 404:
          notification.error('请求的资源不存在')
          break
        case 500:
          businessNotification.serverError()
          break
        default:
          notification.error(message || '请求失败')
      }
    } else if (error.code === 'NETWORK_ERROR') {
      businessNotification.networkError()
    } else {
      notification.error(error.message || '操作失败')
    }
  }
}

export default notification
