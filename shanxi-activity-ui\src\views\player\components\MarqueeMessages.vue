<template>
  <div class="marquee-messages" v-if="messages.length > 0">
    <div class="marquee-container">
      <div class="marquee-header">
        <el-icon class="marquee-icon">
          <Bell />
        </el-icon>
        <span class="marquee-title">中奖播报</span>
      </div>

      <div class="marquee-content">
        <div class="marquee-track" :style="{ transform: `translateX(${translateX}px)` }">
          <div v-for="(message, index) in displayMessages" :key="`${message.id}-${index}`" class="marquee-item">
            <span class="message-text">
              🎉 恭喜 <strong>{{ message.playerNickname }}</strong> 获得
              <strong class="reward-highlight">{{ message.rewardName }}</strong>！
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Bell } from '@element-plus/icons-vue'
import { useDrawStore } from '@/stores'
import type { MarqueeMessage } from '@/types'

interface Props {
  activityId: number
  speed?: number // 滚动速度，像素/秒
  pauseOnHover?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  speed: 50,
  pauseOnHover: true
})

const drawStore = useDrawStore()

const translateX = ref(0)
const containerWidth = ref(0)
const contentWidth = ref(0)
const animationId = ref<number | null>(null)
const isPaused = ref(false)
const lastTimestamp = ref(0)

// 计算属性
const messages = computed(() => drawStore.marqueeMessages)

// 为了无缝滚动，复制消息数组
const displayMessages = computed(() => {
  if (messages.value.length === 0) return []
  // 复制消息以实现无缝循环
  return [...messages.value, ...messages.value]
})

// 方法
const loadMessages = async () => {
  try {
    await drawStore.fetchMarqueeMessages(props.activityId)
  } catch (error) {
    console.error('Failed to load marquee messages:', error)
  }
}

const startAnimation = () => {
  if (animationId.value) return

  const animate = (timestamp: number) => {
    if (!lastTimestamp.value) {
      lastTimestamp.value = timestamp
    }

    if (!isPaused.value) {
      const deltaTime = timestamp - lastTimestamp.value
      const distance = (props.speed * deltaTime) / 1000

      translateX.value -= distance

      // 当滚动到一半时重置位置，实现无缝循环
      if (Math.abs(translateX.value) >= contentWidth.value / 2) {
        translateX.value = 0
      }
    }

    lastTimestamp.value = timestamp
    animationId.value = requestAnimationFrame(animate)
  }

  animationId.value = requestAnimationFrame(animate)
}

const stopAnimation = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }
}

const pauseAnimation = () => {
  isPaused.value = true
}

const resumeAnimation = () => {
  isPaused.value = false
}

const calculateContentWidth = () => {
  // 估算内容宽度，实际项目中可以通过DOM测量
  const averageMessageLength = 50 // 平均消息长度
  const messageWidth = averageMessageLength * 12 // 假设每个字符12px
  contentWidth.value = messages.value.length * messageWidth
}

// 定时刷新消息
const refreshMessages = () => {
  loadMessages()
}

let refreshTimer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  loadMessages()

  // 计算内容宽度
  calculateContentWidth()

  // 开始动画
  if (messages.value.length > 0) {
    startAnimation()
  }

  // 定时刷新消息（每30秒）
  refreshTimer = setInterval(refreshMessages, 30000)
})

onUnmounted(() => {
  stopAnimation()
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 监听消息变化
watch(messages, (newMessages) => {
  calculateContentWidth()

  if (newMessages.length > 0 && !animationId.value) {
    startAnimation()
  } else if (newMessages.length === 0) {
    stopAnimation()
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.marquee-messages {
  position: fixed;
  top: $layout-header-height;
  left: 0;
  right: 0;
  z-index: $z-index-sticky;
  background: $marquee-bg-gradient;
  color: $marquee-text-color;
  height: $marquee-height;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .marquee-container {
    display: flex;
    align-items: center;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 $spacing-lg;

    .marquee-header {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      flex-shrink: 0;
      margin-right: $spacing-lg;

      .marquee-icon {
        font-size: 16px;
        animation: ring 2s infinite;
      }

      .marquee-title {
        font-weight: 600;
        font-size: $font-size-sm;
        white-space: nowrap;
      }
    }

    .marquee-content {
      flex: 1;
      overflow: hidden;
      height: 100%;
      display: flex;
      align-items: center;

      .marquee-track {
        display: flex;
        align-items: center;
        white-space: nowrap;
        will-change: transform;

        .marquee-item {
          margin-right: $spacing-xl;

          .message-text {
            font-size: $font-size-sm;

            strong {
              font-weight: 600;
            }

            .reward-highlight {
              color: #FFD700;
            }
          }
        }
      }
    }
  }

  &:hover {
    .marquee-track {
      animation-play-state: paused;
    }
  }
}

@keyframes ring {

  0%,
  100% {
    transform: rotate(0deg);
  }

  10%,
  30% {
    transform: rotate(-10deg);
  }

  20% {
    transform: rotate(10deg);
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .marquee-messages {
    .marquee-container {
      padding: 0 $spacing-md;

      .marquee-header {
        margin-right: $spacing-md;

        .marquee-title {
          display: none;
        }
      }
    }
  }
}
</style>
