<template>
  <div class="draw-records-list">
    <LoadingSpinner v-if="loading" text="加载中奖记录..." />
    
    <div v-else-if="records.length === 0" class="empty-container">
      <EmptyState 
        type="no-data" 
        title="暂无中奖记录"
        :description="type === 'my' ? '您还没有中奖记录' : '暂时没有中奖记录'"
      />
    </div>
    
    <div v-else class="records-container">
      <div 
        v-for="record in records" 
        :key="record.id"
        class="record-item"
        :class="{ 'is-my-record': record.isMyRecord }"
      >
        <div class="record-avatar">
          <el-avatar :size="32">
            {{ record.playerNickname.charAt(0) }}
          </el-avatar>
        </div>
        
        <div class="record-content">
          <div class="record-info">
            <span class="player-name">{{ record.playerNickname }}</span>
            <span class="reward-name">{{ record.rewardName }}</span>
          </div>
          <div class="record-time">
            {{ formatDateTime(record.drawTime) }}
          </div>
        </div>
        
        <div class="record-badge" v-if="record.isMyRecord">
          <el-tag type="success" size="small">我的</el-tag>
        </div>
      </div>
      
      <!-- 加载更多 -->
      <div class="load-more-container" v-if="hasMore">
        <el-button 
          @click="loadMore"
          :loading="loadingMore"
          size="small"
          text
        >
          加载更多
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { LoadingSpinner, EmptyState } from '@/components/common'
import { useDrawStore } from '@/stores'
import { formatDateTime } from '@/utils'
import type { DrawRecord } from '@/types'

interface Props {
  activityId: number
  type: 'my' | 'all'
  limit?: number
}

const props = withDefaults(defineProps<Props>(), {
  limit: 20
})

const drawStore = useDrawStore()

const loading = ref(false)
const loadingMore = ref(false)
const currentPage = ref(1)

// 计算属性
const records = computed(() => drawStore.drawRecords)
const hasMore = computed(() => records.value.length >= props.limit * currentPage.value)

// 方法
const loadRecords = async (reset = false) => {
  if (reset) {
    loading.value = true
    currentPage.value = 1
  } else {
    loadingMore.value = true
  }
  
  try {
    await drawStore.fetchDrawRecords(props.activityId, props.type, props.limit)
  } catch (error) {
    console.error('Failed to load draw records:', error)
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const loadMore = async () => {
  if (loadingMore.value) return
  
  currentPage.value++
  await loadRecords()
}

// 监听类型变化
watch(() => props.type, () => {
  loadRecords(true)
})

// 生命周期
onMounted(() => {
  loadRecords(true)
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.draw-records-list {
  .empty-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .records-container {
    .record-item {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      padding: $spacing-md;
      border-bottom: 1px solid $border-color-light;
      transition: background-color 0.3s ease;
      
      &:hover {
        background-color: $background-color-light;
      }
      
      &.is-my-record {
        background-color: lighten($primary-color, 45%);
        
        &:hover {
          background-color: lighten($primary-color, 40%);
        }
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .record-avatar {
        flex-shrink: 0;
      }
      
      .record-content {
        flex: 1;
        
        .record-info {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          margin-bottom: $spacing-xs;
          
          .player-name {
            color: $text-color-primary;
            font-weight: 500;
            font-size: $font-size-sm;
          }
          
          .reward-name {
            color: $success-color;
            font-weight: 600;
            font-size: $font-size-sm;
          }
        }
        
        .record-time {
          color: $text-color-secondary;
          font-size: $font-size-sm;
        }
      }
      
      .record-badge {
        flex-shrink: 0;
      }
    }
    
    .load-more-container {
      text-align: center;
      padding: $spacing-lg 0;
    }
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .draw-records-list {
    .record-item {
      padding: $spacing-sm;
      
      .record-content {
        .record-info {
          flex-direction: column;
          align-items: flex-start;
          gap: $spacing-xs;
        }
      }
    }
  }
}
</style>
