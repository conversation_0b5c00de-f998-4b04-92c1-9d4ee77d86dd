<template>
  <div class="activity-list">
    <BaseCard title="活动列表">
      <template #extra>
        <el-button type="primary" @click="$router.push('/activity/create')">
          <el-icon><Plus /></el-icon>
          创建活动
        </el-button>
      </template>
      
      <!-- 搜索和筛选 -->
      <div class="filter-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-input
              v-model="searchParams.search"
              placeholder="搜索活动名称"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          
          <el-col :span="4">
            <el-select 
              v-model="searchParams.status" 
              placeholder="活动状态"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部状态" value="" />
              <el-option label="未开始" value="not_started" />
              <el-option label="进行中" value="running" />
              <el-option label="已结束" value="ended" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-col>
          
          <el-col :span="4">
            <el-select 
              v-model="searchParams.scope" 
              placeholder="活动范围"
              @change="handleFilter"
            >
              <el-option label="我的活动" value="my" />
              <el-option label="全部活动" value="all" />
            </el-select>
          </el-col>
          
          <el-col :span="4">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 活动列表 -->
      <div class="activities-container">
        <LoadingSpinner v-if="loading" text="加载中..." />
        
        <div v-else-if="activities.length === 0" class="empty-container">
          <EmptyState 
            type="no-data" 
            title="暂无活动"
            description="还没有创建任何活动"
          >
            <template #actions>
              <el-button type="primary" @click="$router.push('/activity/create')">
                创建第一个活动
              </el-button>
            </template>
          </EmptyState>
        </div>
        
        <div v-else class="activities-grid">
          <div 
            v-for="activity in activities" 
            :key="activity.id"
            class="activity-card"
          >
            <BaseCard 
              :title="activity.activityName"
              hoverable
              clickable
              @click="handleActivityClick(activity)"
            >
              <template #extra>
                <StatusTag :status="activity.status" />
              </template>
              
              <div class="activity-info">
                <div class="info-row">
                  <span class="label">总通宝：</span>
                  <span class="value">{{ formatTongbao(activity.totalTongbao) }}</span>
                </div>
                
                <div class="info-row">
                  <span class="label">剩余通宝：</span>
                  <span class="value">{{ formatTongbao(activity.remainingTongbao) }}</span>
                </div>
                
                <div class="info-row">
                  <span class="label">参与人数：</span>
                  <span class="value">{{ activity.participantsCount }}</span>
                </div>
                
                <div class="info-row">
                  <span class="label">创建者：</span>
                  <span class="value">{{ activity.creatorNickname }}</span>
                </div>
                
                <div class="time-info">
                  <div class="time-row">
                    <span class="label">开始：</span>
                    <span class="time">{{ formatDateTime(activity.startTime) }}</span>
                  </div>
                  <div class="time-row">
                    <span class="label">结束：</span>
                    <span class="time">{{ formatDateTime(activity.endTime) }}</span>
                  </div>
                </div>
              </div>
              
              <template #footer>
                <div class="activity-actions">
                  <el-button size="small" @click.stop="handleViewDetail(activity)">
                    查看详情
                  </el-button>
                  
                  <el-button 
                    v-if="activity.status === 'running'"
                    size="small" 
                    type="primary"
                    @click.stop="handleViewStatistics(activity)"
                  >
                    数据统计
                  </el-button>
                  
                  <el-button 
                    v-if="canManageActivity(activity)"
                    size="small" 
                    type="warning"
                    @click.stop="handleCloseActivity(activity)"
                    :disabled="activity.status === 'closed'"
                  >
                    关闭活动
                  </el-button>
                </div>
              </template>
            </BaseCard>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="searchParams.page"
          v-model:page-size="searchParams.limit"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { BaseCard, StatusTag, LoadingSpinner, EmptyState } from '@/components/common'
import { useActivityStore, useUserStore } from '@/stores'
import { formatTongbao, formatDateTime, debounce } from '@/utils'
import type { Activity, ActivityListParams } from '@/types'

const router = useRouter()
const activityStore = useActivityStore()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const searchParams = reactive<ActivityListParams>({
  page: 1,
  limit: 20,
  status: undefined,
  scope: 'my',
  search: ''
})

// 计算属性
const activities = computed(() => activityStore.activities)
const total = computed(() => activityStore.total)

// 判断是否可以管理活动
const canManageActivity = (activity: Activity) => {
  return userStore.canManageActivity
}

// 加载活动列表
const loadActivities = async () => {
  loading.value = true
  try {
    await activityStore.fetchActivities(searchParams)
  } catch (error) {
    console.error('Failed to load activities:', error)
    ElMessage.error('加载活动列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理（防抖）
const handleSearch = debounce(() => {
  searchParams.page = 1
  loadActivities()
}, 500)

// 筛选处理
const handleFilter = () => {
  searchParams.page = 1
  loadActivities()
}

// 刷新
const handleRefresh = () => {
  loadActivities()
}

// 分页处理
const handlePageChange = (page: number) => {
  searchParams.page = page
  loadActivities()
}

const handlePageSizeChange = (size: number) => {
  searchParams.limit = size
  searchParams.page = 1
  loadActivities()
}

// 活动操作
const handleActivityClick = (activity: Activity) => {
  handleViewDetail(activity)
}

const handleViewDetail = (activity: Activity) => {
  router.push(`/activity/${activity.id}`)
}

const handleViewStatistics = (activity: Activity) => {
  router.push(`/activity/${activity.id}/statistics`)
}

const handleCloseActivity = async (activity: Activity) => {
  try {
    await ElMessageBox.confirm(
      `确定要关闭活动"${activity.activityName}"吗？关闭后将无法恢复。`,
      '确认关闭',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await activityStore.closeActivity(activity.id)
    ElMessage.success('活动已关闭')
    loadActivities()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to close activity:', error)
      ElMessage.error('关闭活动失败')
    }
  }
}

// 生命周期
onMounted(() => {
  loadActivities()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.activity-list {
  .filter-section {
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-lg;
    border-bottom: 1px solid $border-color-light;
  }
  
  .activities-container {
    min-height: 400px;
    
    .empty-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }
  }
  
  .activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: $spacing-lg;
    
    .activity-card {
      .activity-info {
        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: $spacing-sm;
          
          .label {
            color: $text-color-secondary;
            font-size: $font-size-sm;
          }
          
          .value {
            color: $text-color-primary;
            font-weight: 500;
          }
        }
        
        .time-info {
          margin-top: $spacing-md;
          padding-top: $spacing-md;
          border-top: 1px solid $border-color-light;
          
          .time-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: $spacing-xs;
            
            .label {
              color: $text-color-secondary;
              font-size: $font-size-sm;
            }
            
            .time {
              color: $text-color-primary;
              font-size: $font-size-sm;
            }
          }
        }
      }
      
      .activity-actions {
        display: flex;
        gap: $spacing-sm;
        justify-content: flex-end;
      }
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: $spacing-xl;
  }
}

// 移动端适配
@media (max-width: $screen-md) {
  .activity-list {
    .activities-grid {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }
    
    .filter-section {
      .el-col {
        margin-bottom: $spacing-sm;
      }
    }
  }
}

@media (max-width: $screen-sm) {
  .activity-list {
    .filter-section {
      .el-row {
        flex-direction: column;
        gap: $spacing-sm;
      }
    }
    
    .activity-card {
      .activity-actions {
        flex-direction: column;
        
        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
