<template>
  <el-card
    :class="[
      'base-card',
      {
        'is-hoverable': hoverable,
        'is-clickable': clickable,
        'is-bordered': bordered
      }
    ]"
    :shadow="shadow"
    @click="handleClick"
  >
    <template #header v-if="title || $slots.header">
      <div class="card-header">
        <slot name="header">
          <h3 class="card-title">{{ title }}</h3>
        </slot>
        <div class="card-extra" v-if="$slots.extra">
          <slot name="extra" />
        </div>
      </div>
    </template>
    
    <div class="card-body">
      <slot />
    </div>
    
    <template #footer v-if="$slots.footer">
      <div class="card-footer">
        <slot name="footer" />
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  hoverable?: boolean
  clickable?: boolean
  bordered?: boolean
  shadow?: 'always' | 'hover' | 'never'
}

const props = withDefaults(defineProps<Props>(), {
  hoverable: false,
  clickable: false,
  bordered: true,
  shadow: 'hover'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.base-card {
  border-radius: $border-radius-base;
  
  &.is-hoverable {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  &.is-clickable {
    cursor: pointer;
    
    &:hover {
      border-color: $primary-color;
    }
  }
  
  &.is-bordered {
    border: 1px solid $border-color-light;
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .card-title {
      margin: 0;
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-color-primary;
    }
    
    .card-extra {
      color: $text-color-secondary;
    }
  }
  
  .card-body {
    color: $text-color-primary;
  }
  
  .card-footer {
    border-top: 1px solid $border-color-light;
    padding-top: $spacing-md;
    margin-top: $spacing-md;
  }
}
</style>
