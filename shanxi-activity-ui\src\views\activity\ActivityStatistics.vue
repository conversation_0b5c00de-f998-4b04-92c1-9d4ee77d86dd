<template>
  <div class="activity-statistics">
    <BaseCard title="活动数据统计">
      <template #extra>
        <el-button @click="handleExport" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
      
      <!-- 活动基本信息 -->
      <div class="activity-info" v-if="activity">
        <el-row :gutter="24">
          <el-col :span="18">
            <h3>{{ activity.activityName }}</h3>
            <div class="activity-meta">
              <el-tag :type="getActivityStatusColor(activity.status)">
                {{ getActivityStatusText(activity.status) }}
              </el-tag>
              <span class="meta-item">
                {{ formatDateTime(activity.startTime) }} - {{ formatDateTime(activity.endTime) }}
              </span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="activity-progress">
              <el-progress 
                type="circle" 
                :percentage="progressPercentage"
                :color="getProgressColor(progressPercentage)"
              />
              <div class="progress-text">通宝发放进度</div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 核心统计数据 -->
      <div class="core-statistics">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-statistic 
              title="参与人数" 
              :value="statistics.participantsCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #suffix>
                <el-icon><User /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          
          <el-col :span="6">
            <el-statistic 
              title="总抽奖次数" 
              :value="statistics.totalDraws"
              :value-style="{ color: '#52c41a' }"
            >
              <template #suffix>
                <el-icon><Trophy /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          
          <el-col :span="6">
            <el-statistic 
              title="通宝发放" 
              :value="statistics.totalTongbaoDistributed"
              :formatter="formatTongbao"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #suffix>
                <el-icon><Coin /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          
          <el-col :span="6">
            <el-statistic 
              title="实物奖励" 
              :value="statistics.physicalRewardsCount"
              :value-style="{ color: '#722ed1' }"
            >
              <template #suffix>
                <el-icon><Present /></el-icon>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>
      
      <!-- 图表区域 -->
      <div class="charts-section">
        <el-row :gutter="24">
          <!-- 参与趋势图 -->
          <el-col :span="12">
            <BaseCard title="参与趋势" class="chart-card">
              <div class="chart-container" ref="participationChartRef"></div>
            </BaseCard>
          </el-col>
          
          <!-- 奖励分布图 -->
          <el-col :span="12">
            <BaseCard title="奖励分布" class="chart-card">
              <div class="chart-container" ref="rewardChartRef"></div>
            </BaseCard>
          </el-col>
        </el-row>
        
        <el-row :gutter="24" style="margin-top: 24px;">
          <!-- 时段分析图 -->
          <el-col :span="24">
            <BaseCard title="活跃时段分析" class="chart-card">
              <div class="chart-container large" ref="timeAnalysisChartRef"></div>
            </BaseCard>
          </el-col>
        </el-row>
      </div>
      
      <!-- 玩家排行榜 -->
      <div class="player-rankings">
        <BaseCard title="玩家排行榜">
          <template #extra>
            <el-radio-group v-model="rankingType" size="small">
              <el-radio-button label="draws">抽奖次数</el-radio-button>
              <el-radio-button label="tongbao">获得通宝</el-radio-button>
              <el-radio-button label="physical">实物奖励</el-radio-button>
            </el-radio-group>
          </template>
          
          <el-table :data="topPlayers" stripe>
            <el-table-column type="index" label="排名" width="80">
              <template #default="{ $index }">
                <div class="ranking-badge" :class="getRankingClass($index)">
                  {{ $index + 1 }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="playerNickname" label="玩家昵称" min-width="120" />
            
            <el-table-column prop="totalDraws" label="抽奖次数" width="100" v-if="rankingType === 'draws'" />
            
            <el-table-column prop="totalTongbaoWon" label="获得通宝" width="120" v-if="rankingType === 'tongbao'">
              <template #default="{ row }">
                {{ formatTongbao(row.totalTongbaoWon) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="physicalRewardsCount" label="实物奖励" width="100" v-if="rankingType === 'physical'" />
            
            <el-table-column label="最近活跃" width="120">
              <template #default="{ row }">
                {{ getRelativeTime(row.lastActiveTime) }}
              </template>
            </el-table-column>
          </el-table>
        </BaseCard>
      </div>
      
      <!-- 实物奖励统计 -->
      <div class="physical-rewards-stats" v-if="statistics.physicalRewards.length > 0">
        <BaseCard title="实物奖励统计">
          <el-table :data="statistics.physicalRewards" stripe>
            <el-table-column prop="rewardName" label="奖品名称" min-width="150" />
            <el-table-column prop="totalQuantity" label="总数量" width="100" />
            <el-table-column prop="distributedQuantity" label="已发放" width="100" />
            <el-table-column prop="remainingQuantity" label="剩余" width="100">
              <template #default="{ row }">
                <span :class="{ 'low-stock': row.remainingQuantity < 5 }">
                  {{ row.remainingQuantity }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="发放率" width="100">
              <template #default="{ row }">
                {{ ((row.distributedQuantity / row.totalQuantity) * 100).toFixed(1) }}%
              </template>
            </el-table-column>
          </el-table>
        </BaseCard>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Download, 
  User, 
  Trophy, 
  Coin, 
  Present 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { BaseCard } from '@/components/common'
import { useActivityStore } from '@/stores'
import { 
  formatTongbao, 
  formatDateTime, 
  getActivityStatusText, 
  getActivityStatusColor,
  getRelativeTime 
} from '@/utils'
import type { ActivityStatistics, PlayerStatistic } from '@/types'

const route = useRoute()
const activityStore = useActivityStore()

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const rankingType = ref<'draws' | 'tongbao' | 'physical'>('draws')
const participationChartRef = ref<HTMLElement>()
const rewardChartRef = ref<HTMLElement>()
const timeAnalysisChartRef = ref<HTMLElement>()

// 模拟统计数据
const statistics = ref<ActivityStatistics>({
  participantsCount: 156,
  totalDraws: 1248,
  totalTongbaoDistributed: 125000,
  physicalRewardsCount: 23,
  playerStatistics: [],
  physicalRewards: [
    {
      rewardName: '精美礼品盒',
      totalQuantity: 50,
      distributedQuantity: 23,
      remainingQuantity: 27
    },
    {
      rewardName: '限量纪念品',
      totalQuantity: 20,
      distributedQuantity: 18,
      remainingQuantity: 2
    }
  ]
})

// 计算属性
const activity = computed(() => activityStore.currentActivity)
const activityId = computed(() => Number(route.params.id))

const progressPercentage = computed(() => {
  if (!activity.value) return 0
  const distributed = statistics.value.totalTongbaoDistributed
  const total = activity.value.totalTongbao
  return Math.round((distributed / total) * 100)
})

const topPlayers = computed(() => {
  // 模拟排行榜数据
  const mockPlayers: PlayerStatistic[] = [
    {
      playerId: '1',
      playerNickname: '幸运玩家A',
      totalDraws: 45,
      totalTongbaoWon: 8500,
      physicalRewardsCount: 3,
      lastActiveTime: new Date().toISOString()
    },
    {
      playerId: '2',
      playerNickname: '幸运玩家B',
      totalDraws: 38,
      totalTongbaoWon: 6200,
      physicalRewardsCount: 2,
      lastActiveTime: new Date(Date.now() - 3600000).toISOString()
    }
  ]
  
  return mockPlayers.sort((a, b) => {
    switch (rankingType.value) {
      case 'draws':
        return b.totalDraws - a.totalDraws
      case 'tongbao':
        return b.totalTongbaoWon - a.totalTongbaoWon
      case 'physical':
        return b.physicalRewardsCount - a.physicalRewardsCount
      default:
        return 0
    }
  }).slice(0, 10)
})

// 方法
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#67C23A'
  if (percentage < 70) return '#E6A23C'
  return '#F56C6C'
}

const getRankingClass = (index: number) => {
  if (index === 0) return 'gold'
  if (index === 1) return 'silver'
  if (index === 2) return 'bronze'
  return ''
}

const handleExport = async () => {
  exporting.value = true
  try {
    // 模拟导出
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  } finally {
    exporting.value = false
  }
}

const loadStatistics = async () => {
  loading.value = true
  try {
    // 加载活动详情
    await activityStore.fetchActivityDetail(activityId.value)
    
    // 这里应该调用API加载统计数据
    // await statisticsApi.getActivityStatistics(activityId.value)
    
  } catch (error) {
    console.error('Failed to load statistics:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const initCharts = async () => {
  await nextTick()
  
  // 这里应该初始化图表
  // 由于没有引入图表库，这里只是占位
  console.log('Initialize charts:', {
    participationChart: participationChartRef.value,
    rewardChart: rewardChartRef.value,
    timeAnalysisChart: timeAnalysisChartRef.value
  })
}

// 生命周期
onMounted(() => {
  loadStatistics()
  initCharts()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.activity-statistics {
  .activity-info {
    margin-bottom: $spacing-xl;
    padding-bottom: $spacing-lg;
    border-bottom: 1px solid $border-color-light;
    
    h3 {
      margin: 0 0 $spacing-sm 0;
      color: $text-color-primary;
    }
    
    .activity-meta {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      
      .meta-item {
        color: $text-color-secondary;
        font-size: $font-size-sm;
      }
    }
    
    .activity-progress {
      text-align: center;
      
      .progress-text {
        margin-top: $spacing-sm;
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
    }
  }
  
  .core-statistics {
    margin-bottom: $spacing-xl;
    padding: $spacing-lg;
    background: $background-color-light;
    border-radius: $border-radius-base;
  }
  
  .charts-section {
    margin-bottom: $spacing-xl;
    
    .chart-card {
      height: 300px;
      
      .chart-container {
        height: 200px;
        
        &.large {
          height: 300px;
        }
      }
    }
  }
  
  .player-rankings {
    margin-bottom: $spacing-xl;
    
    .ranking-badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      font-size: $font-size-sm;
      font-weight: 600;
      color: white;
      background: $text-color-secondary;
      
      &.gold {
        background: #FFD700;
        color: #333;
      }
      
      &.silver {
        background: #C0C0C0;
        color: #333;
      }
      
      &.bronze {
        background: #CD7F32;
      }
    }
  }
  
  .physical-rewards-stats {
    .low-stock {
      color: $error-color;
      font-weight: 600;
    }
  }
}

// 移动端适配
@media (max-width: $screen-md) {
  .activity-statistics {
    .activity-info {
      .el-row {
        flex-direction: column;
        gap: $spacing-lg;
      }
    }
    
    .core-statistics {
      .el-col {
        margin-bottom: $spacing-md;
      }
    }
    
    .charts-section {
      .el-col {
        margin-bottom: $spacing-lg;
      }
    }
  }
}
</style>
