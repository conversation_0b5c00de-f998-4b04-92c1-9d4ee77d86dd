import type { useFormItemInputId } from 'element-plus/es/components/form';
import type { CheckboxProps } from '../checkbox';
import type { CheckboxDisabled, CheckboxModel, CheckboxStatus } from '../composables';
export declare const useCheckboxEvent: (props: CheckboxProps, { model, isLimitExceeded, hasOwnLabel, isDisabled, isLabeledByFormItem, }: Pick<CheckboxModel, "model" | "isLimitExceeded"> & Pick<CheckboxStatus, "hasOwnLabel"> & Pick<CheckboxDisabled, "isDisabled"> & Pick<ReturnType<typeof useFormItemInputId>, "isLabeledByFormItem">) => {
    handleChange: (e: Event) => void;
    onClickRoot: (e: MouseEvent) => Promise<void>;
};
