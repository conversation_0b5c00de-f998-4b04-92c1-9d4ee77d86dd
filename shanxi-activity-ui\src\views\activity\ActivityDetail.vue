<template>
  <div class="activity-detail">
    <LoadingSpinner v-if="loading" text="加载活动详情..." />
    
    <div v-else-if="activity" class="detail-content">
      <!-- 活动基础信息 -->
      <BaseCard class="basic-info-card">
        <template #header>
          <div class="card-header">
            <h2>{{ activity.activityName }}</h2>
            <div class="header-actions">
              <StatusTag :status="activity.status" />
              <el-button @click="$router.back()">
                <el-icon><ArrowLeft /></el-icon>
                返回
              </el-button>
            </div>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="活动类型">
                {{ getActivityTypeText(activity.activityType) }}
              </el-descriptions-item>
              <el-descriptions-item label="总通宝数量">
                {{ formatTongbao(activity.totalTongbao) }}
              </el-descriptions-item>
              <el-descriptions-item label="剩余通宝">
                <span :class="{ 'low-balance': isLowBalance }">
                  {{ formatTongbao(activity.remainingTongbao) }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="参与人数">
                {{ activity.participantsCount }}
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="创建者">
                {{ activity.creatorNickname }}
              </el-descriptions-item>
              <el-descriptions-item label="开始时间">
                {{ formatDateTime(activity.startTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="结束时间">
                {{ formatDateTime(activity.endTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="活动状态">
                <StatusTag :status="activity.status" />
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        
        <!-- 活动进度 -->
        <div class="progress-section">
          <h3>活动进度</h3>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-statistic title="通宝使用率" :value="tongbaoUsageRate" suffix="%" />
              <el-progress 
                :percentage="tongbaoUsageRate" 
                :color="getProgressColor(tongbaoUsageRate)"
              />
            </el-col>
            <el-col :span="8">
              <el-statistic title="参与人数" :value="activity.participantsCount" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="活动天数" :value="activityDays" suffix="天" />
            </el-col>
          </el-row>
        </div>
      </BaseCard>
      
      <!-- 奖励配置 -->
      <BaseCard title="奖励配置" class="rewards-card">
        <template #extra>
          <el-button 
            v-if="canEdit" 
            size="small" 
            @click="handleEditRewards"
          >
            编辑奖励
          </el-button>
        </template>
        
        <el-table :data="activity.rewards" border>
          <el-table-column prop="rewardName" label="奖励名称" />
          <el-table-column prop="rewardType" label="类型" width="100">
            <template #default="{ row }">
              <StatusTag :status="row.rewardType === 'tongbao' ? 'success' : 'warning'">
                {{ row.rewardType === 'tongbao' ? '通宝' : '实物' }}
              </StatusTag>
            </template>
          </el-table-column>
          <el-table-column prop="tongbaoAmount" label="通宝数量" width="120">
            <template #default="{ row }">
              {{ formatTongbao(row.tongbaoAmount) }}
            </template>
          </el-table-column>
          <el-table-column prop="probability" label="中奖概率" width="100">
            <template #default="{ row }">
              {{ row.probability?.toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column prop="dailyQuantity" label="每日数量" width="100" />
          <el-table-column prop="remainingQuantity" label="剩余数量" width="100">
            <template #default="{ row }">
              <span :class="{ 'low-quantity': row.remainingQuantity < 10 }">
                {{ row.remainingQuantity || 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="physicalItem" label="实物描述" min-width="150">
            <template #default="{ row }">
              {{ row.physicalItem || '-' }}
            </template>
          </el-table-column>
        </el-table>
      </BaseCard>
      
      <!-- 任务配置 -->
      <BaseCard title="任务配置" class="tasks-card">
        <template #extra>
          <el-button 
            v-if="canEdit" 
            size="small" 
            @click="handleEditTasks"
          >
            编辑任务
          </el-button>
        </template>
        
        <el-table :data="activity.tasks" border>
          <el-table-column prop="taskName" label="任务名称" />
          <el-table-column prop="taskType" label="任务类型" width="120">
            <template #default="{ row }">
              {{ getTaskTypeText(row.taskType) }}
            </template>
          </el-table-column>
          <el-table-column prop="targetValue" label="目标值" width="100" />
          <el-table-column prop="rewardChances" label="奖励次数" width="100" />
          <el-table-column prop="refreshType" label="刷新类型" width="120">
            <template #default="{ row }">
              {{ getRefreshTypeText(row.refreshType) }}
            </template>
          </el-table-column>
        </el-table>
      </BaseCard>
      
      <!-- 管理操作 -->
      <BaseCard v-if="canManage" title="管理操作" class="management-card">
        <div class="management-actions">
          <el-button 
            type="primary" 
            @click="handleViewStatistics"
            :disabled="activity.status === 'not_started'"
          >
            <el-icon><DataAnalysis /></el-icon>
            查看数据统计
          </el-button>
          
          <el-button 
            type="warning" 
            @click="handleCloseActivity"
            :disabled="activity.status === 'closed' || activity.status === 'ended'"
          >
            <el-icon><CircleClose /></el-icon>
            关闭活动
          </el-button>
          
          <el-button 
            type="danger" 
            @click="handleDeleteActivity"
            :disabled="activity.status === 'running'"
          >
            <el-icon><Delete /></el-icon>
            删除活动
          </el-button>
        </div>
      </BaseCard>
    </div>
    
    <EmptyState 
      v-else 
      type="error" 
      title="活动不存在"
      description="该活动可能已被删除或您没有访问权限"
    >
      <template #actions>
        <el-button @click="$router.back()">返回</el-button>
      </template>
    </EmptyState>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  DataAnalysis, 
  CircleClose, 
  Delete 
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  BaseCard, 
  StatusTag, 
  LoadingSpinner, 
  EmptyState 
} from '@/components/common'
import { useActivityStore, useUserStore } from '@/stores'
import { 
  formatTongbao, 
  formatDateTime, 
  getTaskTypeText, 
  getRefreshTypeText 
} from '@/utils'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()
const userStore = useUserStore()

const loading = ref(false)
const activityId = Number(route.params.id)

// 计算属性
const activity = computed(() => activityStore.currentActivity)
const canManage = computed(() => userStore.canManageActivity)
const canEdit = computed(() => {
  return canManage.value && activity.value?.status === 'not_started'
})

const tongbaoUsageRate = computed(() => {
  if (!activity.value) return 0
  const used = activity.value.totalTongbao - activity.value.remainingTongbao
  return Math.round((used / activity.value.totalTongbao) * 100)
})

const isLowBalance = computed(() => {
  if (!activity.value) return false
  return activity.value.remainingTongbao < activity.value.totalTongbao * 0.1
})

const activityDays = computed(() => {
  if (!activity.value) return 0
  const start = new Date(activity.value.startTime)
  const end = new Date(activity.value.endTime)
  return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
})

// 方法
const getActivityTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    blind_box: '盲盒活动'
  }
  return typeMap[type] || type
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#67C23A'
  if (percentage < 70) return '#E6A23C'
  return '#F56C6C'
}

const loadActivityDetail = async () => {
  loading.value = true
  try {
    await activityStore.fetchActivityDetail(activityId)
  } catch (error) {
    console.error('Failed to load activity detail:', error)
    ElMessage.error('加载活动详情失败')
  } finally {
    loading.value = false
  }
}

// 操作处理
const handleEditRewards = () => {
  ElMessage.info('编辑奖励功能开发中')
}

const handleEditTasks = () => {
  ElMessage.info('编辑任务功能开发中')
}

const handleViewStatistics = () => {
  router.push(`/activity/${activityId}/statistics`)
}

const handleCloseActivity = async () => {
  if (!activity.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要关闭活动"${activity.value.activityName}"吗？关闭后将无法恢复。`,
      '确认关闭',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await activityStore.closeActivity(activityId)
    ElMessage.success('活动已关闭')
    loadActivityDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to close activity:', error)
      ElMessage.error('关闭活动失败')
    }
  }
}

const handleDeleteActivity = async () => {
  if (!activity.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除活动"${activity.value.activityName}"吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.info('删除活动功能开发中')
  } catch (error) {
    // 用户取消
  }
}

// 生命周期
onMounted(() => {
  loadActivityDetail()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.activity-detail {
  .detail-content {
    .basic-info-card {
      margin-bottom: $spacing-lg;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h2 {
          margin: 0;
          color: $text-color-primary;
        }
        
        .header-actions {
          display: flex;
          align-items: center;
          gap: $spacing-md;
        }
      }
      
      .progress-section {
        margin-top: $spacing-xl;
        padding-top: $spacing-lg;
        border-top: 1px solid $border-color-light;
        
        h3 {
          margin: 0 0 $spacing-lg 0;
          color: $text-color-primary;
        }
      }
    }
    
    .rewards-card,
    .tasks-card {
      margin-bottom: $spacing-lg;
      
      .low-quantity {
        color: $error-color;
        font-weight: 600;
      }
    }
    
    .management-card {
      .management-actions {
        display: flex;
        gap: $spacing-md;
        flex-wrap: wrap;
      }
    }
    
    .low-balance {
      color: $error-color;
      font-weight: 600;
    }
  }
}

// 移动端适配
@media (max-width: $screen-md) {
  .activity-detail {
    .basic-info-card {
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-md;
        
        .header-actions {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
    
    .management-card {
      .management-actions {
        flex-direction: column;
        
        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
