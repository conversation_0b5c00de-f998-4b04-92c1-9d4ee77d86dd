<template>
  <div class="create-activity">
    <BaseCard title="创建活动">
      <template #extra>
        <el-button @click="$router.back()">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </template>
      
      <!-- 步骤条 -->
      <el-steps :active="currentStep" align-center class="steps-container">
        <el-step title="基础信息" description="设置活动基本信息" />
        <el-step title="奖励配置" description="配置奖励内容和概率" />
        <el-step title="任务设置" description="设置参与任务" />
        <el-step title="确认创建" description="确认信息并创建活动" />
      </el-steps>
      
      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1：基础信息 -->
        <div v-show="currentStep === 0" class="step-panel">
          <BasicInfoForm 
            v-model="formData.basicInfo"
            :errors="formErrors.basicInfo"
            @validate="handleBasicInfoValidate"
          />
        </div>
        
        <!-- 步骤2：奖励配置 -->
        <div v-show="currentStep === 1" class="step-panel">
          <RewardConfigForm 
            v-model="formData.rewards"
            :total-tongbao="formData.basicInfo.totalTongbao"
            :errors="formErrors.rewards"
            @validate="handleRewardsValidate"
          />
        </div>
        
        <!-- 步骤3：任务设置 -->
        <div v-show="currentStep === 2" class="step-panel">
          <TaskConfigForm 
            v-model="formData.tasks"
            :errors="formErrors.tasks"
            @validate="handleTasksValidate"
          />
        </div>
        
        <!-- 步骤4：确认创建 -->
        <div v-show="currentStep === 3" class="step-panel">
          <ConfirmationForm 
            :form-data="formData"
            @create="handleCreateActivity"
          />
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="step-actions">
        <el-button 
          v-if="currentStep > 0" 
          @click="prevStep"
        >
          上一步
        </el-button>
        
        <el-button 
          v-if="currentStep < 3"
          type="primary" 
          @click="nextStep"
          :disabled="!canProceed"
        >
          下一步
        </el-button>
        
        <el-button 
          v-if="currentStep === 3"
          type="primary" 
          @click="handleCreateActivity"
          :loading="creating"
        >
          创建活动
        </el-button>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { BaseCard } from '@/components/common'
import BasicInfoForm from './components/BasicInfoForm.vue'
import RewardConfigForm from './components/RewardConfigForm.vue'
import TaskConfigForm from './components/TaskConfigForm.vue'
import ConfirmationForm from './components/ConfirmationForm.vue'
import { useActivityStore } from '@/stores'
import type { CreateActivityRequest } from '@/types'

const router = useRouter()
const activityStore = useActivityStore()

// 当前步骤
const currentStep = ref(0)
const creating = ref(false)

// 表单数据
const formData = reactive({
  basicInfo: {
    activityName: '',
    activityType: 'blind_box' as const,
    totalTongbao: 0,
    startTime: '',
    endTime: ''
  },
  rewards: [] as any[],
  tasks: [] as any[]
})

// 表单验证错误
const formErrors = reactive({
  basicInfo: {},
  rewards: {},
  tasks: {}
})

// 各步骤验证状态
const stepValidation = reactive({
  basicInfo: false,
  rewards: false,
  tasks: false
})

// 是否可以进入下一步
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return stepValidation.basicInfo
    case 1:
      return stepValidation.rewards
    case 2:
      return stepValidation.tasks
    default:
      return true
  }
})

// 步骤操作
const nextStep = () => {
  if (canProceed.value && currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 验证处理
const handleBasicInfoValidate = (isValid: boolean) => {
  stepValidation.basicInfo = isValid
}

const handleRewardsValidate = (isValid: boolean) => {
  stepValidation.rewards = isValid
}

const handleTasksValidate = (isValid: boolean) => {
  stepValidation.tasks = isValid
}

// 创建活动
const handleCreateActivity = async () => {
  creating.value = true
  try {
    const requestData: CreateActivityRequest = {
      ...formData.basicInfo,
      rewards: formData.rewards,
      tasks: formData.tasks
    }
    
    await activityStore.createActivity(requestData)
    ElMessage.success('活动创建成功！')
    router.push('/activity/list')
  } catch (error) {
    console.error('Failed to create activity:', error)
    ElMessage.error('活动创建失败，请重试')
  } finally {
    creating.value = false
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.create-activity {
  .steps-container {
    margin: $spacing-xl 0;
  }
  
  .step-content {
    min-height: 400px;
    margin: $spacing-xl 0;
  }
  
  .step-panel {
    animation: fadeIn 0.3s ease;
  }
  
  .step-actions {
    display: flex;
    justify-content: center;
    gap: $spacing-md;
    padding-top: $spacing-lg;
    border-top: 1px solid $border-color-light;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .create-activity {
    .steps-container {
      margin: $spacing-lg 0;
    }
    
    .step-content {
      min-height: 300px;
      margin: $spacing-lg 0;
    }
    
    .step-actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
