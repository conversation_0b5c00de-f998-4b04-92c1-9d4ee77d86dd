<template>
  <el-dialog
    v-model="visible"
    title="中奖记录详情"
    width="500px"
    class="record-detail-dialog"
  >
    <div class="record-detail" v-if="record">
      <!-- 奖励展示 -->
      <div class="reward-display">
        <div class="reward-icon">
          <el-icon size="48" :class="rewardIconClass">
            <component :is="rewardIcon" />
          </el-icon>
        </div>
        
        <div class="reward-info">
          <h3 class="reward-name">{{ record.rewardName }}</h3>
          
          <div class="reward-type">
            <StatusTag 
              :status="record.rewardType === 'tongbao' ? 'success' : 'warning'"
            >
              {{ record.rewardType === 'tongbao' ? '通宝奖励' : '实物奖励' }}
            </StatusTag>
          </div>
          
          <div class="reward-value" v-if="record.tongbaoAmount">
            {{ formatTongbao(record.tongbaoAmount) }}
          </div>
          
          <div class="reward-description" v-if="record.physicalItem">
            {{ record.physicalItem }}
          </div>
        </div>
      </div>
      
      <!-- 详细信息 -->
      <div class="detail-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="中奖玩家">
            <div class="player-info">
              <el-avatar :size="24">
                {{ record.playerNickname.charAt(0) }}
              </el-avatar>
              <span class="player-name">{{ record.playerNickname }}</span>
              <el-tag v-if="record.isMyRecord" type="success" size="small">我的</el-tag>
            </div>
          </el-descriptions-item>
          
          <el-descriptions-item label="活动名称">
            {{ record.activityName }}
          </el-descriptions-item>
          
          <el-descriptions-item label="中奖时间">
            {{ formatDateTime(record.drawTime) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="相对时间">
            {{ getRelativeTime(record.drawTime) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="抽奖方式" v-if="record.drawMethod">
            {{ getDrawMethodText(record.drawMethod) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="完成任务" v-if="record.taskName">
            {{ record.taskName }}
          </el-descriptions-item>
          
          <el-descriptions-item label="中奖概率" v-if="record.probability">
            {{ record.probability.toFixed(2) }}%
          </el-descriptions-item>
          
          <el-descriptions-item label="记录ID">
            {{ record.id }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 奖励状态 -->
      <div class="reward-status" v-if="record.rewardType === 'physical'">
        <el-alert
          :title="getRewardStatusText()"
          :type="getRewardStatusType()"
          :description="getRewardStatusDescription()"
          show-icon
          :closable="false"
        />
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="record.isMyRecord">
        <el-button 
          v-if="record.rewardType === 'physical' && !record.claimed"
          type="primary" 
          @click="handleClaimReward"
        >
          <el-icon><Gift /></el-icon>
          领取奖品
        </el-button>
        
        <el-button @click="handleShare">
          <el-icon><Share /></el-icon>
          分享中奖
        </el-button>
        
        <el-button @click="handleDownloadCertificate">
          <el-icon><Download /></el-icon>
          下载证书
        </el-button>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Gift, 
  Share, 
  Download, 
  Coin, 
  Trophy 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { StatusTag } from '@/components/common'
import { formatTongbao, formatDateTime } from '@/utils'
import type { DrawRecord } from '@/types'

interface Props {
  modelValue: boolean
  record: DrawRecord | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const rewardIcon = computed(() => {
  if (!props.record) return Gift
  
  if (props.record.rewardType === 'tongbao') {
    return Coin
  } else if (props.record.tongbaoAmount && props.record.tongbaoAmount >= 1000) {
    return Trophy
  } else {
    return Gift
  }
})

const rewardIconClass = computed(() => [
  'reward-icon-component',
  {
    'icon-tongbao': props.record?.rewardType === 'tongbao',
    'icon-physical': props.record?.rewardType === 'physical',
    'icon-high-value': props.record?.tongbaoAmount && props.record.tongbaoAmount >= 1000
  }
])

// 方法
const getRelativeTime = (dateTime: string) => {
  const now = Date.now()
  const time = new Date(dateTime).getTime()
  const diff = now - time
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  return '很久以前'
}

const getDrawMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    manual: '手动抽奖',
    auto: '自动抽奖',
    task: '任务奖励',
    bonus: '额外奖励'
  }
  return methodMap[method] || method
}

const getRewardStatusText = () => {
  if (!props.record) return ''
  
  if (props.record.claimed) return '奖品已领取'
  if (props.record.rewardType === 'physical') return '待领取奖品'
  return '奖励已发放'
}

const getRewardStatusType = () => {
  if (!props.record) return 'info'
  
  if (props.record.claimed) return 'success'
  if (props.record.rewardType === 'physical') return 'warning'
  return 'success'
}

const getRewardStatusDescription = () => {
  if (!props.record) return ''
  
  if (props.record.claimed) {
    return `奖品已于 ${formatDateTime(props.record.claimedAt || '')} 领取`
  }
  if (props.record.rewardType === 'physical') {
    return '请联系管理员领取实物奖品，或点击领取按钮'
  }
  return '通宝奖励已自动发放到您的账户'
}

const handleClaimReward = () => {
  ElMessage.info('奖品领取功能开发中')
}

const handleShare = () => {
  if (!props.record) return
  
  const shareText = `我在${props.record.activityName}中抽中了${props.record.rewardName}！快来参与吧！`
  
  if (navigator.share) {
    navigator.share({
      title: '中奖分享',
      text: shareText,
      url: window.location.href
    }).catch(() => {
      copyToClipboard(shareText)
    })
  } else {
    copyToClipboard(shareText)
  }
}

const handleDownloadCertificate = () => {
  ElMessage.info('证书下载功能开发中')
}

const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('分享内容已复制到剪贴板')
    })
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.record-detail-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-lg;
  }
  
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    color: white;
    
    .el-dialog__title {
      color: white;
      font-weight: 600;
    }
  }
  
  .record-detail {
    .reward-display {
      text-align: center;
      margin-bottom: $spacing-xl;
      padding: $spacing-lg;
      background: $background-color-light;
      border-radius: $border-radius-base;
      
      .reward-icon {
        margin-bottom: $spacing-md;
        
        .reward-icon-component {
          &.icon-tongbao {
            color: $success-color;
          }
          
          &.icon-physical {
            color: $warning-color;
          }
          
          &.icon-high-value {
            color: $error-color;
            animation: pulse 2s ease-in-out infinite;
          }
        }
      }
      
      .reward-info {
        .reward-name {
          margin: 0 0 $spacing-md 0;
          color: $text-color-primary;
          font-size: $font-size-xl;
        }
        
        .reward-type {
          margin-bottom: $spacing-md;
        }
        
        .reward-value {
          font-size: $font-size-xxl;
          font-weight: 700;
          color: $success-color;
          margin-bottom: $spacing-sm;
        }
        
        .reward-description {
          color: $text-color-secondary;
          font-size: $font-size-sm;
        }
      }
    }
    
    .detail-info {
      margin-bottom: $spacing-lg;
      
      .player-info {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        
        .player-name {
          font-weight: 500;
        }
      }
    }
    
    .reward-status {
      margin-bottom: $spacing-lg;
    }
    
    .action-buttons {
      display: flex;
      gap: $spacing-md;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
  
  .dialog-footer {
    text-align: center;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .record-detail-dialog {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 5vh auto;
    }
    
    .action-buttons {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
