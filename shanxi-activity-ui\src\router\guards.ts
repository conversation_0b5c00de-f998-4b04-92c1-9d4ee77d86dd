import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
import { usePermission } from '@/composables/usePermission'

// 认证守卫
export function setupAuthGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()
    
    // 检查路由是否需要认证
    const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
    
    if (requiresAuth) {
      if (!userStore.isLoggedIn) {
        // 未登录，重定向到登录页
        ElMessage.warning('请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
      
      // 如果已登录但没有用户信息，尝试获取
      if (!userStore.userInfo) {
        try {
          await userStore.fetchUserInfo()
        } catch (error) {
          console.error('Failed to fetch user info:', error)
          ElMessage.error('获取用户信息失败，请重新登录')
          next('/login')
          return
        }
      }
    }
    
    next()
  })
}

// 权限守卫
export function setupPermissionGuard(router: Router) {
  router.beforeEach((to, from, next) => {
    const { canAccessPage, hasAnyRole } = usePermission()
    const userStore = useUserStore()
    
    // 检查页面访问权限
    if (!canAccessPage(to.path)) {
      ElMessage.error('您没有访问该页面的权限')
      
      // 根据用户角色重定向到合适的页面
      if (userStore.userInfo?.role === 'player') {
        next('/player/activities')
      } else if (userStore.userInfo?.role === 'partner') {
        next('/activity/list')
      } else if (userStore.userInfo?.role === 'alliance_leader') {
        next('/activity/list')
      } else {
        next('/login')
      }
      return
    }
    
    // 检查路由元信息中的角色要求
    const requiredRoles = to.meta.roles as string[]
    if (requiredRoles && requiredRoles.length > 0) {
      if (!hasAnyRole(requiredRoles as any)) {
        ElMessage.error('您的角色权限不足')
        next('/403')
        return
      }
    }
    
    next()
  })
}

// 页面标题守卫
export function setupTitleGuard(router: Router) {
  router.afterEach((to) => {
    const title = to.meta.title as string
    if (title) {
      document.title = `${title} - 山西游戏活动系统`
    } else {
      document.title = '山西游戏活动系统'
    }
  })
}

// 路由加载守卫
export function setupLoadingGuard(router: Router) {
  let loadingInstance: any = null
  
  router.beforeEach((to, from, next) => {
    // 显示加载状态
    if (to.meta.showLoading !== false) {
      // 这里可以显示全局加载状态
      console.log('Route loading started:', to.path)
    }
    next()
  })
  
  router.afterEach(() => {
    // 隐藏加载状态
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    console.log('Route loading completed')
  })
  
  router.onError((error) => {
    // 路由错误处理
    console.error('Router error:', error)
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
    ElMessage.error('页面加载失败')
  })
}

// 路由历史守卫
export function setupHistoryGuard(router: Router) {
  const routeHistory: string[] = []
  const maxHistoryLength = 10
  
  router.afterEach((to) => {
    // 记录路由历史
    routeHistory.push(to.path)
    
    // 限制历史记录长度
    if (routeHistory.length > maxHistoryLength) {
      routeHistory.shift()
    }
    
    // 将历史记录存储到sessionStorage
    sessionStorage.setItem('routeHistory', JSON.stringify(routeHistory))
  })
}

// 开发环境守卫
export function setupDevGuard(router: Router) {
  if (import.meta.env.DEV) {
    router.beforeEach((to, from, next) => {
      console.log(`[Router] ${from.path} -> ${to.path}`)
      next()
    })
  }
}

// 设置所有路由守卫
export function setupRouterGuards(router: Router) {
  // 认证守卫
  setupAuthGuard(router)
  
  // 权限守卫
  setupPermissionGuard(router)
  
  // 页面标题守卫
  setupTitleGuard(router)
  
  // 加载状态守卫
  setupLoadingGuard(router)
  
  // 路由历史守卫
  setupHistoryGuard(router)
  
  // 开发环境守卫
  setupDevGuard(router)
}

// 获取路由历史
export function getRouteHistory(): string[] {
  const history = sessionStorage.getItem('routeHistory')
  return history ? JSON.parse(history) : []
}

// 清除路由历史
export function clearRouteHistory() {
  sessionStorage.removeItem('routeHistory')
}
