<template>
  <div class="draw-records">
    <BaseCard title="中奖记录">
      <template #extra>
        <el-radio-group v-model="recordType" size="small">
          <el-radio-button label="my">我的记录</el-radio-button>
          <el-radio-button label="all">全部记录</el-radio-button>
        </el-radio-group>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="filterParams.activityId" placeholder="选择活动" clearable @change="handleFilter">
              <el-option label="全部活动" value="" />
              <el-option v-for="activity in activities" :key="activity.id" :label="activity.activityName"
                :value="activity.id" />
            </el-select>
          </el-col>

          <el-col :span="6">
            <el-select v-model="filterParams.rewardType" placeholder="奖励类型" clearable @change="handleFilter">
              <el-option label="全部类型" value="" />
              <el-option label="通宝奖励" value="tongbao" />
              <el-option label="实物奖励" value="physical" />
            </el-select>
          </el-col>

          <el-col :span="8">
            <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateRangeChange" />
          </el-col>

          <el-col :span="4">
            <el-button @click="handleRefresh">
              <el-icon>
                <Refresh />
              </el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-section" v-if="recordType === 'my'">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-statistic title="总中奖次数" :value="statistics.totalCount" :value-style="{ color: '#1890ff' }" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="获得通宝" :value="statistics.totalTongbao" :formatter="formatTongbao"
              :value-style="{ color: '#52c41a' }" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="实物奖励" :value="statistics.physicalCount" :value-style="{ color: '#fa8c16' }" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="中奖率" :value="statistics.winRate" suffix="%" :value-style="{ color: '#722ed1' }" />
          </el-col>
        </el-row>
      </div>

      <!-- 记录列表 -->
      <div class="records-container">
        <LoadingSpinner v-if="loading" text="加载中奖记录..." />

        <div v-else-if="records.length === 0" class="empty-container">
          <EmptyState type="no-data" title="暂无中奖记录" :description="recordType === 'my' ? '您还没有中奖记录' : '暂时没有中奖记录'" />
        </div>

        <div v-else class="records-list">
          <!-- 表格视图 -->
          <el-table v-if="viewMode === 'table'" :data="records" border stripe class="records-table">
            <el-table-column prop="playerNickname" label="玩家" width="120" />
            <el-table-column prop="activityName" label="活动名称" min-width="150" />
            <el-table-column prop="rewardName" label="奖励名称" min-width="120" />
            <el-table-column prop="rewardType" label="类型" width="80">
              <template #default="{ row }">
                <StatusTag :status="row.rewardType === 'tongbao' ? 'success' : 'warning'" size="small">
                  {{ row.rewardType === 'tongbao' ? '通宝' : '实物' }}
                </StatusTag>
              </template>
            </el-table-column>
            <el-table-column prop="tongbaoAmount" label="通宝数量" width="100">
              <template #default="{ row }">
                {{ row.tongbaoAmount ? formatTongbao(row.tongbaoAmount) : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="drawTime" label="中奖时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.drawTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="handleViewDetail(row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 卡片视图 -->
          <div v-else class="records-cards">
            <div v-for="record in records" :key="record.id" class="record-card"
              :class="{ 'is-my-record': record.isMyRecord }" @click="handleViewDetail(record)">
              <RecordCard :record="record" />
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination v-model:current-page="filterParams.page" v-model:page-size="filterParams.limit" :total="total"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange" @current-change="handlePageChange" />
      </div>
    </BaseCard>

    <!-- 记录详情弹窗 -->
    <RecordDetailDialog v-model="showDetailDialog" :record="selectedRecord" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { BaseCard, StatusTag, LoadingSpinner, EmptyState } from '@/components/common'
import RecordCard from './components/RecordCard.vue'
import RecordDetailDialog from './components/RecordDetailDialog.vue'
import { useDrawStore, useActivityStore } from '@/stores'
import { formatTongbao, formatDateTime, debounce } from '@/utils'
import type { DrawRecord } from '@/types'

const drawStore = useDrawStore()
const activityStore = useActivityStore()

// 响应式数据
const loading = ref(false)
const recordType = ref<'my' | 'all'>('my')
const viewMode = ref<'table' | 'card'>('table')
const dateRange = ref<[string, string] | null>(null)
const showDetailDialog = ref(false)
const selectedRecord = ref<DrawRecord | null>(null)

const filterParams = reactive({
  page: 1,
  limit: 20,
  activityId: '',
  rewardType: '',
  startDate: '',
  endDate: ''
})

// 计算属性
const records = computed(() => drawStore.drawRecords)
const total = computed(() => drawStore.total || 0)
const activities = computed(() => activityStore.activities)

// 统计数据
const statistics = computed(() => {
  if (recordType.value !== 'my') return {}

  const myRecords = records.value.filter(record => record.isMyRecord)
  const totalCount = myRecords.length
  const totalTongbao = myRecords.reduce((sum, record) => sum + (record.tongbaoAmount || 0), 0)
  const physicalCount = myRecords.filter(record => record.rewardType === 'physical').length

  return {
    totalCount,
    totalTongbao,
    physicalCount,
    winRate: totalCount > 0 ? ((totalCount / 100) * 100).toFixed(1) : 0 // 简化计算
  }
})

// 方法
const loadRecords = async () => {
  loading.value = true
  try {
    const params = {
      ...filterParams,
      type: recordType.value
    }
    await drawStore.fetchDrawRecords(params)
  } catch (error) {
    console.error('Failed to load draw records:', error)
  } finally {
    loading.value = false
  }
}

const handleFilter = () => {
  filterParams.page = 1
  loadRecords()
}

const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    filterParams.startDate = dates[0]
    filterParams.endDate = dates[1]
  } else {
    filterParams.startDate = ''
    filterParams.endDate = ''
  }
  handleFilter()
}

const handleRefresh = () => {
  loadRecords()
}

const handlePageChange = (page: number) => {
  filterParams.page = page
  loadRecords()
}

const handlePageSizeChange = (size: number) => {
  filterParams.limit = size
  filterParams.page = 1
  loadRecords()
}

const handleViewDetail = (record: DrawRecord) => {
  selectedRecord.value = record
  showDetailDialog.value = true
}

// 监听记录类型变化
watch(() => recordType.value, () => {
  filterParams.page = 1
  loadRecords()
})

// 生命周期
onMounted(() => {
  loadRecords()
  // 加载活动列表用于筛选
  activityStore.fetchActivities({ page: 1, limit: 100 })
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.draw-records {
  .filter-section {
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-lg;
    border-bottom: 1px solid $border-color-light;
  }

  .statistics-section {
    margin-bottom: $spacing-lg;
    padding: $spacing-lg;
    background: $background-color-light;
    border-radius: $border-radius-base;
  }

  .records-container {
    min-height: 400px;

    .empty-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }

    .records-list {
      .records-table {
        margin-bottom: $spacing-lg;
      }

      .records-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: $spacing-md;

        .record-card {
          cursor: pointer;
          transition: transform 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }

          &.is-my-record {
            border: 2px solid $primary-color;
          }
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: $spacing-xl;
  }
}

// 移动端适配
@media (max-width: $screen-md) {
  .draw-records {
    .filter-section {
      .el-col {
        margin-bottom: $spacing-sm;
      }
    }

    .statistics-section {
      .el-col {
        margin-bottom: $spacing-md;
      }
    }

    .records-cards {
      grid-template-columns: 1fr !important;
    }
  }
}
</style>
