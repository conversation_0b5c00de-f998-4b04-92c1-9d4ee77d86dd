import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { createHtmlPlugin } from 'vite-plugin-html'

// 性能优化配置
export default defineConfig({
  plugins: [
    vue(),
    
    // HTML模板插件
    createHtmlPlugin({
      minify: true,
      inject: {
        data: {
          title: '山西游戏活动系统',
          description: '山西游戏活动管理系统',
          keywords: '游戏,活动,管理,山西'
        }
      }
    }),
    
    // 打包分析插件
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true
    })
  ],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  
  // 构建优化
  build: {
    // 目标浏览器
    target: 'es2015',
    
    // 输出目录
    outDir: 'dist',
    
    // 静态资源目录
    assetsDir: 'assets',
    
    // 小于此阈值的导入或引用资源将内联为base64编码
    assetsInlineLimit: 4096,
    
    // CSS代码分割
    cssCodeSplit: true,
    
    // 生成源码映射
    sourcemap: false,
    
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        // 删除console
        drop_console: true,
        // 删除debugger
        drop_debugger: true,
        // 删除无用代码
        dead_code: true,
        // 删除无用变量
        unused: true
      },
      mangle: {
        // 混淆变量名
        toplevel: true
      }
    },
    
    // Rollup配置
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      
      output: {
        // 代码分割
        manualChunks: {
          // Vue核心
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          
          // Element Plus
          'element-plus': ['element-plus'],
          
          // 图标
          'icons': ['@element-plus/icons-vue'],
          
          // 工具库
          'utils': ['axios', 'dayjs'],
          
          // 业务组件
          'components': [
            './src/components/common/index.ts',
            './src/components/layout/index.ts'
          ]
        },
        
        // 文件命名
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || []
          let extType = info[info.length - 1]
          
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name || '')) {
            extType = 'media'
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name || '')) {
            extType = 'img'
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name || '')) {
            extType = 'fonts'
          }
          
          return `assets/${extType}/[name]-[hash].[ext]`
        }
      },
      
      // 外部依赖（CDN）
      external: [],
      
      // 警告处理
      onwarn(warning, warn) {
        // 忽略某些警告
        if (warning.code === 'UNUSED_EXTERNAL_IMPORT') return
        warn(warning)
      }
    },
    
    // 报告压缩详情
    reportCompressedSize: true,
    
    // chunk大小警告限制
    chunkSizeWarningLimit: 1000
  },
  
  // 开发服务器配置
  server: {
    // 端口
    port: 5174,
    
    // 自动打开浏览器
    open: true,
    
    // 代理配置
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  
  // 预览服务器配置
  preview: {
    port: 4173,
    open: true
  },
  
  // 依赖优化
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus',
      '@element-plus/icons-vue'
    ],
    exclude: []
  },
  
  // CSS配置
  css: {
    // CSS预处理器配置
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    },
    
    // PostCSS配置
    postcss: {
      plugins: [
        // 自动添加浏览器前缀
        require('autoprefixer'),
        
        // CSS压缩
        require('cssnano')({
          preset: 'default'
        })
      ]
    }
  },
  
  // 环境变量
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false
  }
})
