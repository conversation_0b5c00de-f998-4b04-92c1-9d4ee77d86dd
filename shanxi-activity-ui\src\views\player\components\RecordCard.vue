<template>
  <div class="record-card">
    <BaseCard :class="cardClass">
      <template #header>
        <div class="record-header">
          <div class="player-info">
            <el-avatar :size="32" class="player-avatar">
              {{ record.playerNickname.charAt(0) }}
            </el-avatar>
            <div class="player-details">
              <div class="player-name">{{ record.playerNickname }}</div>
              <div class="activity-name">{{ record.activityName }}</div>
            </div>
          </div>
          
          <div class="record-badge" v-if="record.isMyRecord">
            <el-tag type="success" size="small">我的</el-tag>
          </div>
        </div>
      </template>
      
      <div class="record-content">
        <!-- 奖励信息 -->
        <div class="reward-section">
          <div class="reward-icon">
            <el-icon size="32" :class="rewardIconClass">
              <component :is="rewardIcon" />
            </el-icon>
          </div>
          
          <div class="reward-info">
            <h4 class="reward-name">{{ record.rewardName }}</h4>
            
            <div class="reward-type">
              <StatusTag 
                :status="record.rewardType === 'tongbao' ? 'success' : 'warning'"
                size="small"
              >
                {{ record.rewardType === 'tongbao' ? '通宝奖励' : '实物奖励' }}
              </StatusTag>
            </div>
            
            <div class="reward-value" v-if="record.tongbaoAmount">
              {{ formatTongbao(record.tongbaoAmount) }}
            </div>
            
            <div class="reward-description" v-if="record.physicalItem">
              {{ record.physicalItem }}
            </div>
          </div>
        </div>
        
        <!-- 时间信息 -->
        <div class="time-section">
          <div class="time-info">
            <el-icon class="time-icon"><Clock /></el-icon>
            <span class="time-text">{{ formatDateTime(record.drawTime) }}</span>
          </div>
          
          <div class="relative-time">
            {{ getRelativeTime(record.drawTime) }}
          </div>
        </div>
        
        <!-- 额外信息 -->
        <div class="extra-info" v-if="record.drawMethod || record.taskName">
          <div class="info-item" v-if="record.drawMethod">
            <span class="label">抽奖方式：</span>
            <span class="value">{{ getDrawMethodText(record.drawMethod) }}</span>
          </div>
          
          <div class="info-item" v-if="record.taskName">
            <span class="label">完成任务：</span>
            <span class="value">{{ record.taskName }}</span>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="record-actions">
          <el-button size="small" @click="handleViewDetail">
            查看详情
          </el-button>
          
          <el-button 
            v-if="record.rewardType === 'physical' && record.isMyRecord"
            size="small" 
            type="primary"
            @click="handleClaimReward"
          >
            领取奖品
          </el-button>
          
          <el-button 
            v-if="record.isMyRecord"
            size="small" 
            @click="handleShare"
          >
            分享
          </el-button>
        </div>
      </template>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Clock, Coin, Gift, Trophy } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { BaseCard, StatusTag } from '@/components/common'
import { formatTongbao, formatDateTime } from '@/utils'
import type { DrawRecord } from '@/types'

interface Props {
  record: DrawRecord
}

const props = defineProps<Props>()

const emit = defineEmits<{
  viewDetail: [record: DrawRecord]
  claimReward: [record: DrawRecord]
  share: [record: DrawRecord]
}>()

// 计算属性
const cardClass = computed(() => [
  'record-item-card',
  {
    'is-my-record': props.record.isMyRecord,
    'is-high-value': isHighValue.value,
    'is-recent': isRecent.value
  }
])

const rewardIcon = computed(() => {
  if (props.record.rewardType === 'tongbao') {
    return Coin
  } else if (props.record.tongbaoAmount && props.record.tongbaoAmount >= 1000) {
    return Trophy // 高价值奖品
  } else {
    return Gift
  }
})

const rewardIconClass = computed(() => [
  'reward-icon-component',
  {
    'icon-tongbao': props.record.rewardType === 'tongbao',
    'icon-physical': props.record.rewardType === 'physical',
    'icon-high-value': isHighValue.value
  }
])

const isHighValue = computed(() => {
  return props.record.tongbaoAmount && props.record.tongbaoAmount >= 1000
})

const isRecent = computed(() => {
  const drawTime = new Date(props.record.drawTime).getTime()
  const now = Date.now()
  const hoursDiff = (now - drawTime) / (1000 * 60 * 60)
  return hoursDiff <= 24 // 24小时内为最近
})

// 方法
const getRelativeTime = (dateTime: string) => {
  const now = Date.now()
  const time = new Date(dateTime).getTime()
  const diff = now - time
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  return '很久以前'
}

const getDrawMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    manual: '手动抽奖',
    auto: '自动抽奖',
    task: '任务奖励',
    bonus: '额外奖励'
  }
  return methodMap[method] || method
}

const handleViewDetail = () => {
  emit('viewDetail', props.record)
}

const handleClaimReward = () => {
  emit('claimReward', props.record)
  ElMessage.info('奖品领取功能开发中')
}

const handleShare = () => {
  emit('share', props.record)
  
  const shareText = `我在${props.record.activityName}中抽中了${props.record.rewardName}！`
  
  if (navigator.share) {
    navigator.share({
      title: '中奖分享',
      text: shareText,
      url: window.location.href
    }).catch(() => {
      copyToClipboard(shareText)
    })
  } else {
    copyToClipboard(shareText)
  }
}

const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('分享内容已复制到剪贴板')
    })
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.record-card {
  .record-item-card {
    transition: all 0.3s ease;
    
    &.is-my-record {
      border: 2px solid $primary-color;
      background: linear-gradient(135deg, lighten($primary-color, 48%) 0%, $background-color-white 100%);
    }
    
    &.is-high-value {
      border-color: $warning-color;
      box-shadow: 0 4px 12px rgba($warning-color, 0.2);
    }
    
    &.is-recent {
      .record-header {
        .player-info {
          .player-name {
            color: $success-color;
            font-weight: 600;
          }
        }
      }
    }
    
    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .player-info {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        
        .player-avatar {
          border: 2px solid $border-color-light;
        }
        
        .player-details {
          .player-name {
            font-size: $font-size-base;
            font-weight: 500;
            color: $text-color-primary;
            margin-bottom: 2px;
          }
          
          .activity-name {
            font-size: $font-size-sm;
            color: $text-color-secondary;
          }
        }
      }
    }
    
    .record-content {
      .reward-section {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        margin-bottom: $spacing-lg;
        
        .reward-icon {
          flex-shrink: 0;
          
          .reward-icon-component {
            &.icon-tongbao {
              color: $success-color;
            }
            
            &.icon-physical {
              color: $warning-color;
            }
            
            &.icon-high-value {
              color: $error-color;
              animation: pulse 2s ease-in-out infinite;
            }
          }
        }
        
        .reward-info {
          flex: 1;
          
          .reward-name {
            margin: 0 0 $spacing-xs 0;
            font-size: $font-size-lg;
            color: $text-color-primary;
          }
          
          .reward-type {
            margin-bottom: $spacing-sm;
          }
          
          .reward-value {
            font-size: $font-size-xl;
            font-weight: 600;
            color: $success-color;
            margin-bottom: $spacing-xs;
          }
          
          .reward-description {
            font-size: $font-size-sm;
            color: $text-color-secondary;
          }
        }
      }
      
      .time-section {
        margin-bottom: $spacing-md;
        
        .time-info {
          display: flex;
          align-items: center;
          gap: $spacing-xs;
          margin-bottom: $spacing-xs;
          
          .time-icon {
            font-size: 14px;
            color: $text-color-secondary;
          }
          
          .time-text {
            font-size: $font-size-sm;
            color: $text-color-secondary;
          }
        }
        
        .relative-time {
          font-size: $font-size-sm;
          color: $primary-color;
          font-weight: 500;
        }
      }
      
      .extra-info {
        .info-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: $spacing-xs;
          font-size: $font-size-sm;
          
          .label {
            color: $text-color-secondary;
          }
          
          .value {
            color: $text-color-primary;
            font-weight: 500;
          }
        }
      }
    }
    
    .record-actions {
      display: flex;
      gap: $spacing-sm;
      justify-content: flex-end;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .record-card {
    .record-actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
