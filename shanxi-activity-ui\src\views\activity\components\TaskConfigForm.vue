<template>
  <div class="task-config-form">
    <div class="form-header">
      <h3>任务设置</h3>
      <div class="form-tip">
        设置玩家完成任务获得抽奖次数的规则
      </div>
    </div>
    
    <div class="tasks-list">
      <div 
        v-for="(task, index) in tasks" 
        :key="index"
        class="task-item"
      >
        <el-card>
          <template #header>
            <div class="task-header">
              <span>任务 {{ index + 1 }}</span>
              <el-button 
                type="danger" 
                size="small" 
                text
                @click="removeTask(index)"
                :disabled="tasks.length <= 1"
              >
                删除
              </el-button>
            </div>
          </template>
          
          <el-form :model="task" label-width="100px">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="任务类型" required>
                  <el-select 
                    v-model="task.taskType" 
                    style="width: 100%"
                    @change="handleTaskTypeChange(task)"
                  >
                    <el-option label="登录任务" value="login" />
                    <el-option label="局数任务" value="game_rounds" />
                    <el-option label="贡献任务" value="contribution" />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="任务名称" required>
                  <el-input
                    v-model="task.taskName"
                    placeholder="请输入任务名称"
                    maxlength="30"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="getTargetLabel(task.taskType)" required>
                  <el-input-number
                    v-model="task.targetValue"
                    :min="1"
                    :max="getMaxTarget(task.taskType)"
                    style="width: 100%"
                  />
                  <div class="form-tip">
                    {{ getTargetTip(task.taskType) }}
                  </div>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="奖励次数" required>
                  <el-input-number
                    v-model="task.rewardChances"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                  <div class="form-tip">
                    完成任务获得的抽奖次数
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="刷新类型" required>
                  <el-select 
                    v-model="task.refreshType" 
                    style="width: 100%"
                  >
                    <el-option label="每日刷新" value="daily" />
                    <el-option label="每周刷新" value="weekly" />
                    <el-option label="不刷新" value="never" />
                  </el-select>
                  <div class="form-tip">
                    {{ getRefreshTip(task.refreshType) }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    
    <div class="form-actions">
      <el-button @click="addTask" :disabled="tasks.length >= 5">
        <el-icon><Plus /></el-icon>
        添加任务
      </el-button>
      
      <div class="validation-info">
        <el-alert
          v-if="validationMessage"
          :title="validationMessage"
          :type="isValid ? 'success' : 'warning'"
          show-icon
          :closable="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import type { Task, TaskType, RefreshType } from '@/types'

const props = defineProps<{
  modelValue: Partial<Task>[]
  errors?: Record<string, string>
}>()

const emit = defineEmits<{
  'update:modelValue': [value: Partial<Task>[]]
  validate: [isValid: boolean]
}>()

const tasks = ref<Partial<Task>[]>([...props.modelValue])

// 如果没有任务，添加默认任务
if (tasks.value.length === 0) {
  tasks.value.push({
    taskType: 'login',
    taskName: '每日登录',
    targetValue: 1,
    rewardChances: 1,
    refreshType: 'daily'
  })
}

// 验证状态
const isValid = computed(() => {
  const hasEmptyFields = tasks.value.some(task => 
    !task.taskType || 
    !task.taskName || 
    !task.targetValue || 
    !task.rewardChances || 
    !task.refreshType
  )
  
  return !hasEmptyFields && tasks.value.length > 0
})

// 验证消息
const validationMessage = computed(() => {
  if (tasks.value.length === 0) {
    return '请至少添加一个任务'
  }
  
  const hasEmptyFields = tasks.value.some(task => 
    !task.taskType || !task.taskName || !task.targetValue || !task.rewardChances || !task.refreshType
  )
  
  if (hasEmptyFields) {
    return '请完善所有任务的必填信息'
  }
  
  return '任务配置完成'
})

// 添加任务
const addTask = () => {
  if (tasks.value.length < 5) {
    tasks.value.push({
      taskType: 'login',
      taskName: '',
      targetValue: 1,
      rewardChances: 1,
      refreshType: 'daily'
    })
  }
}

// 删除任务
const removeTask = (index: number) => {
  if (tasks.value.length > 1) {
    tasks.value.splice(index, 1)
  }
}

// 处理任务类型变化
const handleTaskTypeChange = (task: Partial<Task>) => {
  const defaultNames = {
    login: '每日登录',
    game_rounds: '完成对局',
    contribution: '贡献积分'
  }
  
  const defaultTargets = {
    login: 1,
    game_rounds: 5,
    contribution: 100
  }
  
  task.taskName = defaultNames[task.taskType as TaskType]
  task.targetValue = defaultTargets[task.taskType as TaskType]
}

// 获取目标值标签
const getTargetLabel = (taskType?: TaskType) => {
  const labels = {
    login: '登录天数',
    game_rounds: '对局数量',
    contribution: '贡献积分'
  }
  return labels[taskType || 'login']
}

// 获取目标值提示
const getTargetTip = (taskType?: TaskType) => {
  const tips = {
    login: '需要连续登录的天数',
    game_rounds: '需要完成的对局数量',
    contribution: '需要贡献的积分数量'
  }
  return tips[taskType || 'login']
}

// 获取最大目标值
const getMaxTarget = (taskType?: TaskType) => {
  const maxValues = {
    login: 30,
    game_rounds: 100,
    contribution: 10000
  }
  return maxValues[taskType || 'login']
}

// 获取刷新类型提示
const getRefreshTip = (refreshType?: RefreshType) => {
  const tips = {
    daily: '每天重置任务进度',
    weekly: '每周重置任务进度',
    never: '任务完成后不再重置'
  }
  return tips[refreshType || 'daily']
}

// 监听数据变化
watch(tasks, (newValue) => {
  emit('update:modelValue', [...newValue])
}, { deep: true })

watch(isValid, (valid) => {
  emit('validate', valid)
})

// 初始验证
nextTick(() => {
  emit('validate', isValid.value)
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.task-config-form {
  .form-header {
    margin-bottom: $spacing-lg;
    
    h3 {
      margin: 0 0 $spacing-sm 0;
      color: $text-color-primary;
    }
    
    .form-tip {
      font-size: $font-size-sm;
      color: $text-color-secondary;
    }
  }
  
  .tasks-list {
    .task-item {
      margin-bottom: $spacing-lg;
      
      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
      }
      
      .form-tip {
        font-size: $font-size-sm;
        color: $text-color-secondary;
        margin-top: $spacing-xs;
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-xl;
    
    .validation-info {
      flex: 1;
      margin-left: $spacing-lg;
    }
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .task-config-form {
    .form-actions {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-md;
      
      .validation-info {
        margin-left: 0;
      }
    }
  }
}
</style>
