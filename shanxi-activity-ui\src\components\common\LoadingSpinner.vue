<template>
  <div :class="[
    'loading-spinner',
    {
      'is-fullscreen': fullscreen,
      'is-overlay': overlay
    }
  ]" v-show="visible">
    <div class="spinner-content">
      <el-icon class="spinner-icon" :size="iconSize">
        <Loading />
      </el-icon>
      <p class="spinner-text" v-if="text">{{ text }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Loading } from '@element-plus/icons-vue'

interface Props {
  visible?: boolean
  text?: string
  fullscreen?: boolean
  overlay?: boolean
  size?: 'small' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  fullscreen: false,
  overlay: true,
  size: 'default'
})

const iconSize = computed(() => {
  const sizeMap = {
    small: 24,
    default: 32,
    large: 48
  }
  return sizeMap[props.size]
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;

  &.is-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-index-modal;
  }

  &.is-overlay {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
  }

  .spinner-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-md;

    .spinner-icon {
      color: $primary-color;
      animation: spin 1s linear infinite;
    }

    .spinner-text {
      margin: 0;
      color: $text-color-secondary;
      font-size: $font-size-sm;
      text-align: center;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
